<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Welcome Header -->
        <div class="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary-600 to-primary-800 dark:from-primary-700 dark:to-primary-900 p-6">
            <div class="relative flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <div class="rounded-full bg-white/20 dark:bg-white/10 p-3">
                        <x-heroicon-s-user-circle class="h-10 w-10" />
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">
                            {{ $greeting }}, {{ Auth::guard('client')->user()->name }}!
                        </h1>
                        <p class="text-primary-100 dark:text-primary-200">{{ $currentTime->format('l, F j, Y') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-5">
            @foreach([
                ['active_contracts', 'العقود السارية', 'heroicon-s-check-circle', 'success'],
                ['pending_contracts', 'قيد المعالجة', 'heroicon-s-clock', 'warning'],
                ['completed_contracts', 'المكتملة', 'heroicon-s-document-check', 'info'],
                ['total_contracts', 'إجمالي العقود', 'heroicon-s-document-text', 'gray'],
                ['upcoming_visits', 'الزيارات القادمة', 'heroicon-s-calendar', 'primary']
            ] as [$key, $label, $icon, $color])
                <x-filament::card class="group transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $label }}</h2>
                            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">{{ $this->getStats()[$key] }}</p>
                        </div>
                        <div class="rounded-full bg-{{ $color }}-100 p-3 text-{{ $color }}-600 dark:bg-{{ $color }}-900/30 dark:text-{{ $color }}-400">
                            <x-dynamic-component :component="$icon" class="h-5 w-5" />
                        </div>
                    </div>
                </x-filament::card>
            @endforeach
        </div>

        <!-- Notifications -->
        @if(count($this->getNotifications()) > 0)
            <x-filament::section heading="الإشعارات">
                <div class="space-y-3">
                    @foreach($this->getNotifications() as $notification)
                        <div class="flex items-start space-x-3 rtl:space-x-reverse rounded-lg border p-4
                        {{ $notification['type'] === 'warning' ? 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/20' : 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20' }}">
                            <div class="flex-shrink-0">
                                @if($notification['type'] === 'warning')
                                    <x-heroicon-s-exclamation-triangle class="h-5 w-5 text-amber-500 dark:text-amber-400" />
                                @else
                                    <x-heroicon-s-information-circle class="h-5 w-5 text-blue-500 dark:text-blue-400" />
                                @endif
                            </div>
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100">{{ $notification['title'] }}</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-300">{{ $notification['message'] }}</p>
                            </div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">منذ {{ $notification['time'] }}</span>
                        </div>
                    @endforeach
                </div>
            </x-filament::section>
        @endif

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <!-- Upcoming Visits -->
            <x-filament::section heading="الزيارات القادمة">
                @if(count($this->getUpcomingVisits()) > 0)
                    <div class="space-y-4">
                        @foreach($this->getUpcomingVisits() as $visit)
                            <x-filament::card>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                        <div class="rounded-full bg-primary-100 p-2 text-primary-600 dark:bg-primary-900/30 dark:text-primary-400">
                                            <x-heroicon-s-wrench-screwdriver class="h-5 w-5" />
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-gray-900 dark:text-gray-100">زيارة صيانة دورية</h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $visit['contract_number'] }}</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $visit['technician'] }}</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-gray-900 dark:text-gray-100">{{ $visit['visit_date'] }}</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-300">{{ $visit['visit_time'] }}</div>
                                        <x-filament::badge color="info" size="sm">
                                            {{ $visit['status_label'] }}
                                        </x-filament::badge>
                                    </div>
                                </div>
                            </x-filament::card>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <x-heroicon-o-calendar class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد زيارات قادمة</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">لم يتم جدولة أي زيارات صيانة</p>
                    </div>
                @endif
            </x-filament::section>

            <!-- Recent Contracts -->
            <x-filament::section heading="أحدث العقود">
                @if(count($this->getRecentContracts()) > 0)
                    <div class="space-y-3">
                        @foreach($this->getRecentContracts() as $contract)
                            <x-filament::card>
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="font-medium text-gray-900 dark:text-gray-100">{{ $contract->request_number }}</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-300">{{ $contract->contractType->name ?? 'غير محدد' }}</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $contract->created_at->format('d/m/Y') }}</p>
                                    </div>
                                    <div class="flex flex-col items-end space-y-2">
                                        @php
                                            $statusConfig = match($contract->status) {
                                                'new' => ['color' => 'info', 'label' => 'جديد'],
                                                'pending' => ['color' => 'warning', 'label' => 'قيد المراجعة'],
                                                'approved' => ['color' => 'success', 'label' => 'معتمد'],
                                                'rejected' => ['color' => 'danger', 'label' => 'مرفوض'],
                                                'completed' => ['color' => 'gray', 'label' => 'مكتمل'],
                                                default => ['color' => 'gray', 'label' => $contract->status],
                                            };
                                        @endphp
                                        <x-filament::badge color="{{ $statusConfig['color'] }}" size="sm">
                                            {{ $statusConfig['label'] }}
                                        </x-filament::badge>
                                        <x-filament::button
                                            tag="a"
                                            href="{{ route('filament.client.resources.maintenance-contracts.view', $contract) }}"
                                            color="primary"
                                            size="xs"
                                        >
                                            عرض
                                        </x-filament::button>
                                    </div>
                                </div>
                            </x-filament::card>
                        @endforeach
                    </div>
                    <div class="mt-4 text-center">
                        <x-filament::button
                            tag="a"
                            href="{{ route('filament.client.resources.maintenance-contracts.index') }}"
                            color="gray"
                            size="sm"
                        >
                            عرض جميع العقود
                        </x-filament::button>
                    </div>
                @else
                    <div class="text-center py-8">
                        <x-heroicon-o-document-text class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" />
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">لا توجد عقود</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">ابدأ بإنشاء أول عقد صيانة</p>
                        <div class="mt-4">
                            <x-filament::button
                                tag="a"
                                href="{{ route('filament.client.resources.maintenance-contracts.wizard.step1') }}"
                                color="primary"
                                size="sm"
                            >
                                إنشاء عقد جديد
                            </x-filament::button>
                        </div>
                    </div>
                @endif
            </x-filament::section>
        </div>

        <!-- Quick Actions -->
        <x-filament::section heading="الخدمات السريعة">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                @foreach([
                    ['طلب عقد صيانة', 'إنشاء طلب عقد صيانة جديد', 'heroicon-s-document-plus', 'primary', route('filament.client.resources.maintenance-contracts.wizard.step1')],
                    ['طلب شهادة فحص', 'احصل على شهادات فحص معتمدة', 'heroicon-s-shield-check', 'success', '#'],
                    ['الدعم الفني', 'تواصل مع فريق الدعم', 'heroicon-s-phone', 'info', '#']
                ] as [$title, $description, $icon, $color, $url])
                    <x-filament::card class="group cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
                        <a href="{{ $url }}" class="block text-center">
                            <div class="mx-auto mb-3 rounded-full bg-{{ $color }}-100 p-3 text-{{ $color }}-600 dark:bg-{{ $color }}-900/30 dark:text-{{ $color }}-400 w-fit">
                                <x-dynamic-component :component="$icon" class="h-6 w-6" />
                            </div>
                            <h3 class="font-medium text-gray-900 dark:text-gray-100 group-hover:text-{{ $color }}-600 dark:group-hover:text-{{ $color }}-400 transition-colors">{{ $title }}</h3>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">{{ $description }}</p>
                        </a>
                    </x-filament::card>
                @endforeach
            </div>
        </x-filament::section>

        <!-- Footer Contact -->
        <x-filament::section>
            <div class="rounded-lg bg-gray-50 dark:bg-gray-800/50 p-6 text-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">هل تحتاج مساعدة؟</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700 dark:text-gray-300">
                    <div class="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <x-heroicon-s-phone class="h-4 w-4 text-primary-600 dark:text-primary-400" />
                        <span>+966 123 456 789</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <x-heroicon-s-envelope class="h-4 w-4 text-primary-600 dark:text-primary-400" />
                        <span><EMAIL></span>
                    </div>
                    <div class="flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <x-heroicon-s-clock class="h-4 w-4 text-primary-600 dark:text-primary-400" />
                        <span>الأحد - الخميس: 8 ص - 5 م</span>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
