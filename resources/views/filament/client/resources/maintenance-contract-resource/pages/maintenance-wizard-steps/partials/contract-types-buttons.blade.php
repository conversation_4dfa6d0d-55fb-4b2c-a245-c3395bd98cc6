<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
    @foreach($contractTypes as $type)
        <div class="contract-type-card group relative">
            <button
                type="button"
                wire:click="selectContractType('{{ $type['slug'] }}')"
                class="w-full h-full p-6 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 rounded-xl hover:border-primary-500 hover:shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-right"
            >
                {{-- Header with Icon and Title --}}
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                            {{ $type['name'] }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                            {{ $type['description'] }}
                        </p>
                    </div>
                    @if(isset($type['icon']))
                        <div class="mr-4 flex-shrink-0">
                            <x-filament::icon
                                :icon="$type['icon']"
                                class="w-8 h-8 text-primary-500"
                            />
                        </div>
                    @endif
                </div>

                {{-- Benefits List --}}
                @if(!empty($type['benefits']) && is_array($type['benefits']))
                    <div class="mb-4">
                        <ul class="space-y-2">
                            @foreach(array_slice($type['benefits'], 0, 3) as $benefit)
                                <li class="flex items-start text-sm text-gray-600 dark:text-gray-400">
                                    <x-filament::icon
                                        icon="heroicon-s-check-circle"
                                        class="w-4 h-4 text-green-500 mt-0.5 ml-2 flex-shrink-0"
                                    />
                                    <span>{{ $benefit }}</span>
                                </li>
                            @endforeach
                            @if(count($type['benefits']) > 3)
                                <li class="text-xs text-gray-500 mr-6">
                                    وـ {{ count($type['benefits']) - 3 }} مزايا أخرى...
                                </li>
                            @endif
                        </ul>
                    </div>
                @endif

                {{-- Contract Details --}}
                <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-xs text-gray-500">مدة العقد</span>
                        <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                            {{ $type['period'] }} شهر
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-500">عدد الزيارات</span>
                        <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                            {{ $type['visit_limit'] }} زيارة
                        </span>
                    </div>
                </div>

                {{-- Hover Effect Indicator --}}
                <div class="absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <x-filament::icon
                        icon="heroicon-s-arrow-left"
                        class="w-5 h-5 text-primary-500"
                    />
                </div>

                {{-- Selection Ripple Effect --}}
                <div class="absolute inset-0 bg-primary-500 bg-opacity-5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </button>
        </div>
    @endforeach
</div>

{{-- Loading State --}}
<div wire:loading wire:target="selectContractType" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-3 rtl:space-x-reverse">
        <x-filament::loading-indicator class="w-5 h-5" />
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">جاري الانتقال للخطوة التالية...</span>
    </div>
</div>

<style>
    .contract-type-card:hover {
        transform: translateY(-2px);
    }

    .contract-type-card button:focus {
        transform: translateY(-1px);
    }

    .contract-type-card button:active {
        transform: translateY(0);
    }
</style>
