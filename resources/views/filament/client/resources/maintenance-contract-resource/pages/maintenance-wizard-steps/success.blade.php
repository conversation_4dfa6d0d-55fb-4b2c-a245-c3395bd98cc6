<x-filament-panels::page>
    <x-filament::section>
        <x-slot name="heading">
            {{ __('filament-resources/maintenance-request.wizard.success.step_heading', [], 'الخطوة 4 من 4: تم بنجاح') }}
        </x-slot>

        <x-slot name="description">
            {{ __('filament-resources/maintenance-request.wizard.success.step_description', [], 'تم إرسال طلب عقد الصيانة بنجاح') }}
        </x-slot>

        <div class="text-center py-12">
            <div class="mb-6">
                <x-filament::icon
                    icon="heroicon-o-check-circle"
                    class="w-20 h-20 text-success-500 mx-auto"
                />
            </div>

            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                {{ __('filament-resources/maintenance-request.wizard.success.main_heading', [], 'تم إرسال طلب عقد الصيانة بنجاح!') }}
            </h2>

            <div class="space-y-4 mb-8">
                <p class="text-lg text-gray-600 dark:text-gray-400">
                    {{ __('filament-resources/maintenance-request.wizard.success.request_number_label', [], 'رقم الطلب:') }}
                    <span class="font-bold text-primary-600">{{ $record->request_number }}</span>
                </p>

                {{-- Status Badge --}}
                <div class="flex items-center justify-center gap-2">
                    <span class="text-gray-600 dark:text-gray-400">{{ __('filament-resources/maintenance-request.wizard.success.current_status_label', [], 'الحالة الحالية:') }}</span>
                    <x-filament::badge :color="$this->getStatusColor()">
                        {{ $this->getStatusText() }}
                    </x-filament::badge>
                </div>

                {{-- Status Description --}}
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                    <p class="text-blue-800 dark:text-blue-200 text-center">
                        {{ $statusDescription }}
                    </p>
                </div>

                {{-- Next Steps --}}
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4">
                    <h4 class="font-semibold text-green-800 dark:text-green-200 mb-2">
                        {{ __('filament-resources/maintenance-request.wizard.success.next_steps_title', [], 'الخطوات التالية') }}
                    </h4>
                    <p class="text-green-700 dark:text-green-300">
                        {{ $this->getNextSteps() }}
                    </p>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <x-filament::button
                    wire:click="startNewRequest"
                    color="primary"
                    icon="heroicon-o-plus"
                    class="flex-1"
                >
                    {{ __('filament-resources/maintenance-request.wizard.success.actions.new_request', [], 'طلب عقد جديد') }}
                </x-filament::button>

                <x-filament::button
                    wire:click="viewRequests"
                    color="gray"
                    icon="heroicon-o-document-text"
                    class="flex-1"
                >
                    {{ __('filament-resources/maintenance-request.wizard.success.actions.view_requests', [], 'عرض الطلبات') }}
                </x-filament::button>
            </div>
        </div>

        {{-- Request Details Card --}}
        <x-filament::section class="mt-8">
            <x-slot name="heading">
                {{ __('filament-resources/maintenance-request.wizard.success.details_section_title', [], 'تفاصيل الطلب') }}
            </x-slot>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('filament-resources/maintenance-request.wizard.success.details.request_number', [], 'رقم الطلب:') }}</span>
                        <span class="font-mono text-primary-600 dark:text-primary-400">{{ $record->request_number }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('filament-resources/maintenance-request.wizard.success.details.status', [], 'حالة الطلب:') }}</span>
                        <x-filament::badge :color="$this->getStatusColor()">
                            {{ $this->getStatusText() }}
                        </x-filament::badge>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('filament-resources/maintenance-request.wizard.success.details.created_date', [], 'تاريخ الطلب:') }}</span>
                        <span>{{ $record->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('filament-resources/maintenance-request.wizard.success.details.contract_type', [], 'نوع العقد:') }}</span>
                        <span>{{ $record->contractType->name ?? __('filament-resources/maintenance-request.wizard.success.details.not_specified', [], 'غير محدد') }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('filament-resources/maintenance-request.wizard.success.details.visits_included', [], 'عدد الزيارات:') }}</span>
                        <span class="font-semibold text-green-600 dark:text-green-400">{{ $record->visits_included }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ __('filament-resources/maintenance-request.wizard.success.details.client', [], 'العميل:') }}</span>
                        <span>{{ $record->client->name ?? __('filament-resources/maintenance-request.wizard.success.details.not_specified', [], 'غير محدد') }}</span>
                    </div>
                </div>
            </div>

            {{-- Status Timeline Preview --}}
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h4 class="font-semibold text-gray-900 dark:text-white mb-4">
                    {{ __('filament-resources/maintenance-request.wizard.success.timeline_title', [], 'مراحل معالجة الطلب') }}
                </h4>
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    {{-- Current Status --}}
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 bg-warning-500 text-white rounded-full flex items-center justify-center">
                            <x-filament::icon icon="heroicon-s-clock" class="w-4 h-4" />
                        </div>
                        <span class="text-xs mt-1 text-warning-600 font-medium">{{ __('filament-resources/maintenance-request.status_options.new', [], 'طلب جديد') }}</span>
                    </div>

                    {{-- Arrow --}}
                    <div class="flex-1 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                    {{-- Next Status --}}
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center">
                            <x-filament::icon icon="heroicon-s-eye" class="w-4 h-4" />
                        </div>
                        <span class="text-xs mt-1 text-gray-500">{{ __('filament-resources/maintenance-request.status_options.pending', [], 'قيد المراجعة') }}</span>
                    </div>

                    {{-- Arrow --}}
                    <div class="flex-1 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                    {{-- Future Status --}}
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center">
                            <x-filament::icon icon="heroicon-s-user-plus" class="w-4 h-4" />
                        </div>
                        <span class="text-xs mt-1 text-gray-500">{{ __('filament-resources/maintenance-request.status_options.assigned', [], 'تم التعيين') }}</span>
                    </div>

                    {{-- Arrow --}}
                    <div class="flex-1 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                    {{-- Final Status --}}
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-500 rounded-full flex items-center justify-center">
                            <x-filament::icon icon="heroicon-s-check-badge" class="w-4 h-4" />
                        </div>
                        <span class="text-xs mt-1 text-gray-500">{{ __('filament-resources/maintenance-request.status_options.completed', [], 'مكتمل') }}</span>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </x-filament::section>
</x-filament-panels::page>
