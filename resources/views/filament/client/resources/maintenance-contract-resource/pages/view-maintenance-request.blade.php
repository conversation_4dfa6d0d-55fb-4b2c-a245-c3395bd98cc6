<x-filament-panels::page>
    {{-- Status Banner --}}
    @php
        $statusConfig = match($record->status) {
            'new' => ['color' => 'warning', 'icon' => 'heroicon-o-clock', 'text' => 'طلب جديد'],
            'pending' => ['color' => 'info', 'icon' => 'heroicon-o-eye', 'text' => 'قيد المراجعة'],
            'assigned' => ['color' => 'primary', 'icon' => 'heroicon-o-user-plus', 'text' => 'تم التعيين'],
            'in_progress' => ['color' => 'warning', 'icon' => 'heroicon-o-cog-6-tooth', 'text' => 'قيد التنفيذ'],
            'completed' => ['color' => 'success', 'icon' => 'heroicon-o-check-badge', 'text' => 'مكتمل'],
            'canceled' => ['color' => 'gray', 'icon' => 'heroicon-o-x-mark', 'text' => 'ملغي'],
            default => ['color' => 'gray', 'icon' => 'heroicon-o-question-mark-circle', 'text' => $record->status],
        };
    @endphp

    <div class="mb-6">
        <x-filament::section>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                        <x-filament::icon
                            :icon="$statusConfig['icon']"
                            class="w-8 h-8 text-{{ $statusConfig['color'] }}-500"
                        />
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $record->request_number }}
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                تم الإنشاء في {{ $record->created_at->format('d/m/Y H:i') }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <x-filament::badge
                        :color="$statusConfig['color']"
                        size="lg"
                    >
                        {{ $statusConfig['text'] }}
                    </x-filament::badge>

                    @if($record->contractType)
                        <div class="mt-2">
                            <x-filament::badge color="gray">
                                {{ $record->contractType->name }}
                            </x-filament::badge>
                        </div>
                    @endif
                </div>
            </div>
        </x-filament::section>
    </div>

    {{-- Progress Tracker --}}
    <div class="mb-8">
        <x-filament::section>
            <x-slot name="heading">
                تتبع مراحل الطلب
            </x-slot>

            <div class="relative">
                <div class="flex items-center justify-between">
                    {{-- Step 1: Request Created --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center">
                            <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium text-green-600">إنشاء الطلب</p>
                            <p class="text-xs text-gray-500">{{ $record->created_at->format('d/m') }}</p>
                        </div>
                    </div>

                    {{-- Progress Line 1 --}}
                    <div class="flex-1 h-1 mx-4 {{ in_array($record->status, ['pending', 'assigned', 'in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 2: Under Review --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ in_array($record->status, ['assigned', 'in_progress', 'completed']) ? 'bg-green-500 text-white' : ($record->status === 'pending' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') }} rounded-full flex items-center justify-center">
                            @if(in_array($record->status, ['assigned', 'in_progress', 'completed']))
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @elseif($record->status === 'pending')
                                <x-filament::icon icon="heroicon-s-clock" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ in_array($record->status, ['assigned', 'in_progress', 'completed']) ? 'text-green-600' : ($record->status === 'pending' ? 'text-yellow-600' : 'text-gray-500') }}">المراجعة</p>
                            <p class="text-xs text-gray-500">
                                @if(in_array($record->status, ['assigned', 'in_progress', 'completed']))
                                    تمت المراجعة
                                @elseif($record->status === 'pending')
                                    قيد المعالجة
                                @else
                                    في الانتظار
                                @endif
                            </p>
                        </div>
                    </div>

                    {{-- Progress Line 2 --}}
                    <div class="flex-1 h-1 mx-4 {{ in_array($record->status, ['assigned', 'in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 3: Technician Assignment --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ in_array($record->status, ['assigned', 'in_progress', 'completed']) ? 'bg-green-500 text-white' : ($record->status === 'pending' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') }} rounded-full flex items-center justify-center">
                            @if(in_array($record->status, ['assigned', 'in_progress', 'completed']))
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @elseif($record->status === 'pending')
                                <x-filament::icon icon="heroicon-s-clock" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ in_array($record->status, ['assigned', 'in_progress', 'completed']) ? 'text-green-600' : ($record->status === 'pending' ? 'text-yellow-600' : 'text-gray-500') }}">تعيين الفني</p>
                            <p class="text-xs text-gray-500">
                                @if(in_array($record->status, ['assigned', 'in_progress', 'completed']))
                                    تم التعيين
                                @elseif($record->status === 'pending')
                                    قيد التعيين
                                @else
                                    في الانتظار
                                @endif
                            </p>
                        </div>
                    </div>

                    {{-- Progress Line 3 --}}
                    <div class="flex-1 h-1 mx-4 {{ in_array($record->status, ['in_progress', 'completed']) ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 4: Work in Progress --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ in_array($record->status, ['in_progress', 'completed']) ? 'bg-green-500 text-white' : ($record->status === 'assigned' ? 'bg-yellow-500 text-white' : 'bg-gray-300 text-gray-600') }} rounded-full flex items-center justify-center">
                            @if(in_array($record->status, ['in_progress', 'completed']))
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @elseif($record->status === 'assigned')
                                <x-filament::icon icon="heroicon-s-clock" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ in_array($record->status, ['in_progress', 'completed']) ? 'text-green-600' : ($record->status === 'assigned' ? 'text-yellow-600' : 'text-gray-500') }}">تنفيذ العمل</p>
                            <p class="text-xs text-gray-500">
                                @if($record->status === 'completed')
                                    تم الإنجاز
                                @elseif($record->status === 'in_progress')
                                    قيد التنفيذ
                                @elseif($record->status === 'assigned')
                                    سيبدأ قريباً
                                @else
                                    في الانتظار
                                @endif
                            </p>
                        </div>
                    </div>

                    {{-- Progress Line 4 --}}
                    <div class="flex-1 h-1 mx-4 {{ $record->status === 'completed' ? 'bg-green-500' : 'bg-gray-300' }}"></div>

                    {{-- Step 5: Completion --}}
                    <div class="flex flex-col items-center">
                        <div class="w-10 h-10 {{ $record->status === 'completed' ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600' }} rounded-full flex items-center justify-center">
                            @if($record->status === 'completed')
                                <x-filament::icon icon="heroicon-s-check" class="w-5 h-5" />
                            @else
                                <x-filament::icon icon="heroicon-s-ellipsis-horizontal" class="w-5 h-5" />
                            @endif
                        </div>
                        <div class="mt-2 text-center">
                            <p class="text-xs font-medium {{ $record->status === 'completed' ? 'text-green-600' : 'text-gray-500' }}">الإنجاز</p>
                            <p class="text-xs text-gray-500">
                                @if($record->status === 'completed')
                                    مكتمل
                                @else
                                    في الانتظار
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>

    {{-- Main Content --}}
    {{ $this->infolist }}

    {{-- Quick Actions Footer --}}
    @if(in_array($record->status, ['new', 'pending']))
        <div class="mt-8">
            <x-filament::section>
                <x-slot name="heading">
                    إجراءات سريعة
                </x-slot>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <x-filament::card>
                        <div class="text-center p-4">
                            <x-filament::icon icon="heroicon-o-phone" class="w-8 h-8 text-blue-500 mx-auto mb-2" />
                            <h3 class="font-medium text-gray-900 dark:text-white">التواصل معنا</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">للاستفسار عن حالة طلبك</p>
                            <x-filament::button
                                wire:click="$dispatch('open-modal', { id: 'contact-support' })"
                                color="primary"
                                size="sm"
                            >
                                تواصل الآن
                            </x-filament::button>
                        </div>
                    </x-filament::card>

                    <x-filament::card>
                        <div class="text-center p-4">
                            <x-filament::icon icon="heroicon-o-document-text" class="w-8 h-8 text-green-500 mx-auto mb-2" />
                            <h3 class="font-medium text-gray-900 dark:text-white">تحديث البيانات</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">تعديل معلومات الطلب</p>
                            <x-filament::button
                                href="{{ route('filament.client.resources.maintenance-contracts.edit', $record) }}"
                                color="success"
                                size="sm"
                            >
                                تعديل
                            </x-filament::button>
                        </div>
                    </x-filament::card>

                    <x-filament::card>
                        <div class="text-center p-4">
                            <x-filament::icon icon="heroicon-o-printer" class="w-8 h-8 text-gray-500 mx-auto mb-2" />
                            <h3 class="font-medium text-gray-900 dark:text-white">طباعة الطلب</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">احصل على نسخة مطبوعة</p>
                            <x-filament::button
                                href="{{ route('client.maintenance-request.print', $record) }}"
                                target="_blank"
                                color="gray"
                                size="sm"
                            >
                                طباعة
                            </x-filament::button>
                        </div>
                    </x-filament::card>
                </div>
            </x-filament::section>
        </div>
    @endif

    {{-- Help Section --}}
    <div class="mt-8">
        <x-filament::section>
            <x-slot name="heading">
                هل تحتاج مساعدة؟
            </x-slot>

            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
                <div class="flex items-start space-x-4 rtl:space-x-reverse">
                    <x-filament::icon icon="heroicon-o-information-circle" class="w-6 h-6 text-blue-500 mt-1" />
                    <div class="flex-1">
                        <h3 class="font-medium text-blue-900 dark:text-blue-100 mb-2">معلومات مفيدة</h3>
                        <div class="space-y-2 text-sm text-blue-800 dark:text-blue-200">
                            <p>• <strong>وقت المعالجة:</strong> عادة ما يتم مراجعة الطلبات خلال 1-3 أيام عمل</p>
                            <p>• <strong>تعيين الفني:</strong> سيتم تعيين فني مختص بعد الموافقة على الطلب</p>
                            <p>• <strong>تنفيذ العمل:</strong> سيتواصل معك الفني لتحديد موعد مناسب للصيانة</p>
                            <p>• <strong>التواصل:</strong> سنتواصل معك عبر الهاتف أو البريد الإلكتروني المسجل</p>
                            <p>• <strong>العقد:</strong> سيتم إنشاء العقد تلقائياً بعد اعتماد الطلب</p>
                        </div>

                        <div class="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
                            <div class="flex flex-wrap gap-4 text-sm">
                                <a href="tel:+966123456789" class="flex items-center text-blue-600 dark:text-blue-400 hover:underline">
                                    <x-filament::icon icon="heroicon-o-phone" class="w-4 h-4 ml-1" />
                                    123-456-789
                                </a>
                                <a href="mailto:<EMAIL>" class="flex items-center text-blue-600 dark:text-blue-400 hover:underline">
                                    <x-filament::icon icon="heroicon-o-envelope" class="w-4 h-4 ml-1" />
                                    <EMAIL>
                                </a>
                                <span class="flex items-center text-blue-600 dark:text-blue-400">
                                    <x-filament::icon icon="heroicon-o-clock" class="w-4 h-4 ml-1" />
                                    الأحد - الخميس: 8:00 ص - 5:00 م
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>
</x-filament-panels::page>
