<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Completes the polymorphic conversion by removing the old contract_id column.
     */
    public function up(): void
    {
        Schema::table('visits', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['contract_id']);
            
            // Drop the contract_id column
            $table->dropColumn('contract_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * Recreates the contract_id column for rollback.
     */
    public function down(): void
    {
        Schema::table('visits', function (Blueprint $table) {
            // Recreate the contract_id column
            $table->foreignId('contract_id')->nullable()->after('id')
                  ->constrained('contracts')->nullOnDelete();
        });

        // Migrate data back from polymorphic to contract_id
        // Only migrate visits that were originally contract-based
        DB::statement("
            UPDATE visits 
            SET contract_id = visitable_id
            WHERE visitable_type = 'App\\\\Models\\\\Contract'
        ");
    }
};
