<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Converts the visits table to use polymorphic relationships,
     * allowing visits to be associated with either contracts or maintenance requests.
     */
    public function up(): void
    {
        Schema::table('visits', function (Blueprint $table) {
            // Add polymorphic columns
            $table->string('visitable_type')->nullable()->after('contract_id');
            $table->unsignedBigInteger('visitable_id')->nullable()->after('visitable_type');

            // Add maintenance_request_id for backward compatibility
            $table->foreignId('maintenance_request_id')->nullable()->after('visitable_id')
                  ->constrained('maintenance_requests')->nullOnDelete();

            // Add index for polymorphic relationship
            $table->index(['visitable_type', 'visitable_id'], 'idx_visits_visitable');

            // Add index for maintenance_request_id
            $table->index('maintenance_request_id', 'idx_visits_maintenance_request');
        });

        // Migrate existing data to polymorphic structure
        // All existing visits are contract-based
        DB::statement("
            UPDATE visits
            SET visitable_type = 'App\\\\Models\\\\Contract',
                visitable_id = contract_id
            WHERE contract_id IS NOT NULL
        ");

        // After data migration, remove the old contract_id column
        Schema::table('visits', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['contract_id']);

            // Drop the contract_id column
            $table->dropColumn('contract_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * Reverts the polymorphic structure back to contract-only visits.
     */
    public function down(): void
    {
        Schema::table('visits', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex('idx_visits_visitable');
            $table->dropIndex('idx_visits_maintenance_request');
            
            // Drop foreign key constraint
            $table->dropForeign(['maintenance_request_id']);
            
            // Drop polymorphic and maintenance_request_id columns
            $table->dropColumn(['visitable_type', 'visitable_id', 'maintenance_request_id']);
        });
    }
};
