<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\MaintenanceRequest;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * PDF Storage Service
 *
 * Handles PDF file storage operations for maintenance requests including:
 * - File storage with configurable disks
 * - Unique filename generation
 * - File cleanup and management
 * - Error handling and logging
 * - Disk-agnostic operations for future S3 migration
 *
 * @package App\Services
 * <AUTHOR>
 * @version 1.0.0
 */
class PdfStorageService
{
    /**
     * Default storage disk for PDF files
     */
    private const DEFAULT_DISK = 'public';

    /**
     * PDF storage directory
     */
    private const PDF_DIRECTORY = 'maintenance-requests/pdfs';

    /**
     * Maximum file size for PDF files (in bytes) - 10MB
     */
    private const MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * Store PDF content to disk and update maintenance request record.
     *
     * @param MaintenanceRequest $maintenanceRequest
     * @param string $pdfContent
     * @param string|null $disk
     * @return array{success: bool, url: string|null, path: string|null, error: string|null}
     */
    public static function storePdf(
        MaintenanceRequest $maintenanceRequest,
        string $pdfContent,
        ?string $disk = null
    ): array {
        try {
            $disk = $disk ?? static::getConfiguredDisk();
            $storage = Storage::disk($disk);

            // Validate PDF content size
            if (strlen($pdfContent) > static::MAX_FILE_SIZE) {
                throw new Exception('PDF file size exceeds maximum allowed size of ' . static::formatBytes(static::MAX_FILE_SIZE));
            }

            // Generate unique file path
            $filePath = static::generateFilePath($maintenanceRequest);

            // Ensure directory exists
            static::ensureDirectoryExists($storage, dirname($filePath));

            // Delete old PDF if exists
            static::deleteOldPdf($maintenanceRequest);

            // Store the PDF file
            $stored = $storage->put($filePath, $pdfContent);

            if (!$stored) {
                throw new Exception('Failed to store PDF file to disk');
            }

            // Update maintenance request with PDF info
            $updated = $maintenanceRequest->storePdfInfo($filePath, $disk);

            if (!$updated) {
                // Cleanup the file if database update failed
                $storage->delete($filePath);
                throw new Exception('Failed to update maintenance request with PDF information');
            }

            // Get the public URL
            $url = $storage->url($filePath);

            Log::info('PDF stored successfully', [
                'maintenance_request_id' => $maintenanceRequest->id,
                'file_path' => $filePath,
                'disk' => $disk,
                'file_size' => strlen($pdfContent),
                'url' => $url
            ]);

            return [
                'success' => true,
                'url' => $url,
                'path' => $filePath,
                'error' => null
            ];

        } catch (Exception $e) {
            Log::error('PDF storage failed', [
                'maintenance_request_id' => $maintenanceRequest->id,
                'error' => $e->getMessage(),
                'disk' => $disk ?? static::getConfiguredDisk(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'url' => null,
                'path' => null,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete PDF file and clear database references.
     *
     * @param MaintenanceRequest $maintenanceRequest
     * @return bool
     */
    public static function deletePdf(MaintenanceRequest $maintenanceRequest): bool
    {
        try {
            $deleted = $maintenanceRequest->deletePdf();

            Log::info('PDF deleted', [
                'maintenance_request_id' => $maintenanceRequest->id,
                'success' => $deleted
            ]);

            return $deleted;

        } catch (Exception $e) {
            Log::error('PDF deletion failed', [
                'maintenance_request_id' => $maintenanceRequest->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get PDF URL if file exists and is valid.
     *
     * @param MaintenanceRequest $maintenanceRequest
     * @return string|null
     */
    public static function getPdfUrl(MaintenanceRequest $maintenanceRequest): ?string
    {
        if (!$maintenanceRequest->hasPdf()) {
            return null;
        }

        return $maintenanceRequest->pdf_url;
    }

    /**
     * Check if PDF exists and is valid.
     *
     * @param MaintenanceRequest $maintenanceRequest
     * @return bool
     */
    public static function pdfExists(MaintenanceRequest $maintenanceRequest): bool
    {
        return $maintenanceRequest->hasPdf();
    }

    /**
     * Get configured storage disk for PDF files.
     *
     * @return string
     */
    public static function getConfiguredDisk(): string
    {
        return config('filesystems.pdf_disk', static::DEFAULT_DISK);
    }

    /**
     * Generate unique file path for PDF.
     *
     * @param MaintenanceRequest $maintenanceRequest
     * @return string
     */
    private static function generateFilePath(MaintenanceRequest $maintenanceRequest): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "maintenance_request_{$maintenanceRequest->id}_{$timestamp}.pdf";

        return static::PDF_DIRECTORY . '/' . $filename;
    }

    /**
     * Ensure directory exists on the storage disk.
     *
     * @param \Illuminate\Contracts\Filesystem\Filesystem $storage
     * @param string $directory
     * @return void
     */
    private static function ensureDirectoryExists($storage, string $directory): void
    {
        if (!$storage->exists($directory)) {
            $storage->makeDirectory($directory);
        }
    }

    /**
     * Delete old PDF file if it exists.
     *
     * @param MaintenanceRequest $maintenanceRequest
     * @return void
     */
    private static function deleteOldPdf(MaintenanceRequest $maintenanceRequest): void
    {
        if ($maintenanceRequest->hasPdf()) {
            try {
                Storage::disk($maintenanceRequest->pdf_disk)->delete($maintenanceRequest->pdf_file_path);

                Log::info('Old PDF deleted', [
                    'maintenance_request_id' => $maintenanceRequest->id,
                    'old_path' => $maintenanceRequest->pdf_file_path
                ]);
            } catch (Exception $e) {
                Log::warning('Failed to delete old PDF', [
                    'maintenance_request_id' => $maintenanceRequest->id,
                    'old_path' => $maintenanceRequest->pdf_file_path,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Format bytes to human-readable format.
     *
     * @param int $bytes
     * @return string
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $power = floor(log($bytes, 1024));

        return round($bytes / pow(1024, $power), 2) . ' ' . $units[$power];
    }

    /**
     * Clean up old PDF files (for maintenance/cleanup jobs).
     *
     * @param int $daysOld
     * @return int Number of files cleaned up
     */
    public static function cleanupOldPdfs(int $daysOld = 30): int
    {
        try {
            $cutoffDate = now()->subDays($daysOld);
            $disk = static::getConfiguredDisk();
            $storage = Storage::disk($disk);

            // Get maintenance requests with old PDFs
            $oldRequests = MaintenanceRequest::where('pdf_generated_at', '<', $cutoffDate)
                ->whereNotNull('pdf_file_path')
                ->get();

            $cleanedCount = 0;

            foreach ($oldRequests as $request) {
                if ($storage->exists($request->pdf_file_path)) {
                    $storage->delete($request->pdf_file_path);
                    $cleanedCount++;
                }

                // Clear database references
                $request->update([
                    'pdf_file_path' => null,
                    'pdf_disk' => null,
                    'pdf_generated_at' => null,
                ]);
            }

            Log::info('PDF cleanup completed', [
                'files_cleaned' => $cleanedCount,
                'cutoff_date' => $cutoffDate,
                'disk' => $disk
            ]);

            return $cleanedCount;

        } catch (Exception $e) {
            Log::error('PDF cleanup failed', [
                'error' => $e->getMessage(),
                'days_old' => $daysOld
            ]);

            return 0;
        }
    }
}
