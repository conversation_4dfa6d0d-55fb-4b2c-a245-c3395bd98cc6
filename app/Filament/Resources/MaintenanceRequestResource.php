<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use Carbon\Carbon;
use App\Filament\Resources\MaintenanceRequestResource\Pages;
use App\Filament\Resources\MaintenanceRequestResource\RelationManagers;
use App\Filament\Traits\HasMaintenanceRequestFormHelpers;
use App\Filament\Traits\HasMaintenanceRequestTableHelpers;
use App\Models\MaintenanceRequest;
use App\Models\Contract;
use App\Models\ContractType;
use App\Models\Client;
use App\Models\User;
use App\Models\Payment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Database\Eloquent\Model;
use Filament\GlobalSearch\Actions\Action;

/**
 * MaintenanceRequestResource
 *
 * Comprehensive Filament resource for managing maintenance requests with enhanced features:
 * - Optimized database queries with eager loading
 * - Advanced caching strategies for improved performance
 * - Enhanced user experience with loading states and accessibility
 * - Comprehensive internationalization support
 * - Role-based permissions and granular access control
 * - Responsive design with RTL language support
 *
 * @package App\Filament\Resources
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
class MaintenanceRequestResource extends Resource
{
    use HasMaintenanceRequestFormHelpers, HasMaintenanceRequestTableHelpers;

    /**
     * The Eloquent model associated with the resource.
     */
    protected static ?string $model = MaintenanceRequest::class;

    /**
     * The navigation icon for the resource.
     */
    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    /**
     * Cache TTL for resource data (in minutes).
     */
    protected static int $cacheMinutes = 10;

    /**
     * Get the navigation group for the resource.
     *
     * @return string The translated navigation group name
     */
    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.maintenance_management');
    }

    /**
     * Get the navigation sort order.
     */
    protected static ?int $navigationSort = 1;

    /**
     * Get the model label for the resource.
     *
     * @return string The translated model label
     */
    public static function getModelLabel(): string
    {
        return __('navigation.resources.maintenance_request.label');
    }

    /**
     * Get the plural model label for the resource.
     *
     * @return string The translated plural model label
     */
    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.maintenance_request.plural');
    }

    /**
     * Get the navigation badge for the resource.
     *
     * @return string|null The count of pending maintenance requests
     */
    public static function getNavigationBadge(): ?string
    {
        $cacheKey = 'maintenance_requests_pending_count_' . Auth::id();

        return Cache::remember($cacheKey, now()->addMinutes(5), function () {
            return (string) MaintenanceRequest::where('status', MaintenanceRequest::STATUS_NEW)
                ->orWhere('status', MaintenanceRequest::STATUS_ASSIGNED)
                ->count();
        });
    }

    /**
     * Get the navigation badge color.
     *
     * @return string|array|null The badge color
     */
    public static function getNavigationBadgeColor(): string|array|null
    {
        $count = (int) static::getNavigationBadge();

        return match (true) {
            $count === 0 => 'success',
            $count <= 5 => 'warning',
            default => 'danger',
        };
    }

    /**
     * Define the form schema for maintenance requests.
     *
     * @param Form $form The form instance
     * @return Form The configured form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                static::getMainFormSection(),
                static::getAdditionalDetailsSection(),
            ])
            ->columns(2)
            ->extraAttributes([
                'class' => 'maintenance-request-form',
                'dir' => app()->getLocale() === 'ar' ? 'rtl' : 'ltr',
            ]);
    }

    /**
     * Get the main form section with core fields.
     *
     * @return Forms\Components\Section
     */
    protected static function getMainFormSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make(__('filament-resources/maintenance-request.sections.main_details'))
            ->description(__('filament-resources/maintenance-request.sections.main_details_description'))
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        static::getRequestNumberField(),
                        static::getContractTypeField(),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        static::getClientField(),
                        static::getAssignedTechnicianField(),
                    ]),
                static::getStatusField(),
                static::getCompletionDateField(),
            ])
            ->columnSpan(1)
            ->collapsible()
            ->persistCollapsed()
            ->extraAttributes([
                'class' => 'main-form-section',
            ]);
    }

    /**
     * Get the additional details section.
     *
     * @return Forms\Components\Section
     */
    protected static function getAdditionalDetailsSection(): Forms\Components\Section
    {
        return Forms\Components\Section::make(__('filament-resources/maintenance-request.sections.additional_details'))
            ->description(__('filament-resources/maintenance-request.sections.additional_details_description'))
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label(__('filament-resources/maintenance-request.fields.title'))
                    ->required()
                    ->maxLength(255)
                    ->helperText(__('filament-resources/maintenance-request.fields.title_help'))
                    ->extraAttributes([
                        'aria-label' => __('filament-resources/maintenance-request.fields.title'),
                    ]),
                Forms\Components\Textarea::make('description')
                    ->label(__('filament-resources/maintenance-request.fields.description'))
                    ->required()
                    ->rows(4)
                    ->maxLength(1000)
                    ->helperText(__('filament-resources/maintenance-request.fields.description_help'))
                    ->extraAttributes([
                        'aria-label' => __('filament-resources/maintenance-request.fields.description'),
                        'dir' => app()->getLocale() === 'ar' ? 'rtl' : 'ltr',
                    ]),
                static::getNotesField(),
                Forms\Components\Hidden::make('request_date')
                    ->default(now()),
            ])
            ->columnSpan(1)
            ->collapsible()
            ->persistCollapsed()
            ->extraAttributes([
                'class' => 'additional-details-section',
            ]);
    }

    /**
     * Define the table configuration for maintenance requests.
     *
     * @param Table $table The table instance
     * @return Table The configured table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns(static::getTableColumns())
            ->filters(static::getTableFilters())
            ->filtersFormColumns(3)
            ->filtersFormWidth('4xl')
            ->persistFiltersInSession()
            ->defaultSort('created_at', 'desc')
            ->poll('30s') // Auto-refresh every 30 seconds
            ->striped()
            /*->extraAttributes([
                'class' => 'maintenance-request-table',
                'dir' => app()->getLocale() === 'ar' ? 'rtl' : 'ltr',
            ])*/
            ->actions([
                static::getViewAction(),
                static::getEditAction(),
                static::getUpdatePriceAction(),
                static::getCreateContractAction(),
                static::getDeleteAction(),
            ])

            ->bulkActions(static::getBulkActions())
            ->emptyStateHeading(__('filament-resources/maintenance-request.empty_state.heading'))
            ->emptyStateDescription(__('filament-resources/maintenance-request.empty_state.description'))
            ->emptyStateIcon('heroicon-o-wrench-screwdriver');
    }

    /**
     * Get the view action for table rows.
     *
     * @return Tables\Actions\ViewAction
     */
    protected static function getViewAction(): Tables\Actions\ViewAction
    {
        return Tables\Actions\ViewAction::make()
            ->label(__('filament-resources/maintenance-request.actions.view'))
            ->modalHeading(fn (MaintenanceRequest $record): string =>
                __('filament-resources/maintenance-request.actions.view_modal_heading', ['number' => $record->request_number])
            );
    }

    /**
     * Get the edit action for table rows.
     *
     * @return Tables\Actions\EditAction
     */
    protected static function getEditAction(): Tables\Actions\EditAction
    {
        return Tables\Actions\EditAction::make()
            ->label(__('filament-resources/maintenance-request.actions.edit'))
            ->visible(fn (MaintenanceRequest $record): bool =>
                Auth::user()->can('update', $record)
            );
    }

    /**
     * Get the update price action for table rows.
     *
     * @return Tables\Actions\Action
     */
    protected static function getUpdatePriceAction(): Tables\Actions\Action
    {
        return Tables\Actions\Action::make('update_price')
            ->label(__('filament-resources/maintenance-request.actions.update_price'))
            ->icon('heroicon-o-currency-dollar')
            ->color('success')
            ->visible(fn (MaintenanceRequest $record): bool =>
                is_null($record->price) && Auth::user()->can('update-price', $record)
            )
            ->form([
                Forms\Components\TextInput::make('price')
                    ->label(__('filament-resources/maintenance-request.fields.contract_price'))
                    ->required()
                    ->numeric()
                    ->prefix(__('filament-resources/maintenance-request.currency.sar'))
                    ->minValue(0)
                    ->helperText(__('filament-resources/maintenance-request.fields.contract_price_help')),
                Forms\Components\Textarea::make('price_notes')
                    ->label(__('filament-resources/maintenance-request.fields.price_notes'))
                    ->rows(3)
                    ->maxLength(500),
            ])
            ->action(function (MaintenanceRequest $record, array $data): void {
                try {
                    // Update the maintenance request price
                    $record->update(['price' => $data['price']]);

                    // Create a payment record
                    $payment = new Payment();
                    $payment->maintenance_request_id = $record->id;
                    $payment->amount = $data['price'];
                    $payment->due_date = now()->addDays(30);
                    $payment->status = __('filament-resources/maintenance-request.payment_status.pending');
                    $payment->notes = $data['price_notes'] ?? null;

                    // Generate payment number
                    $latestPayment = Payment::latest('id')->first();
                    $nextId = $latestPayment ? $latestPayment->id + 1 : 1;
                    $payment->payment_number = 'PAY-' . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);

                    $payment->save();

                    Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.price_updated'))
                        ->body(__('filament-resources/maintenance-request.notifications.price_updated_body', ['amount' => $data['price']]))
                        ->success()
                        ->send();

                } catch (\Exception $e) {
                    Log::error('Error updating maintenance request price: ' . $e->getMessage());

                    Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.price_update_failed'))
                        ->body(__('filament-resources/maintenance-request.notifications.price_update_failed_body'))
                        ->danger()
                        ->send();
                }
            });
    }

    /**
     * Get the create contract action for table rows.
     *
     * @return Tables\Actions\Action
     */
    protected static function getCreateContractAction(): Tables\Actions\Action
    {
        return Tables\Actions\Action::make('create_contract')
            ->label(__('filament-resources/maintenance-request.actions.create_contract'))
            ->icon('heroicon-o-document-text')
            ->color('primary')
            ->visible(fn (MaintenanceRequest $record): bool =>
                $record->status === 'paid' &&
                !$record->contract_id &&
                Auth::user()->can('create', Contract::class)
            )
            ->form([
                Forms\Components\TextInput::make('contract_number')
                    ->label(__('filament-resources/contract.fields.contract_number'))
                    ->required()
                    ->maxLength(255)
                    ->unique('contracts', 'contract_number')
                    ->default(function (): string {
                        $latestContract = Contract::latest('id')->first();
                        $nextId = $latestContract ? $latestContract->id + 1 : 1;
                        return 'CNT-' . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);
                    }),
                Forms\Components\DatePicker::make('start_date')
                    ->label(__('filament-resources/contract.fields.start_date'))
                    ->required()
                    ->default(now()),
                Forms\Components\DatePicker::make('end_date')
                    ->label(__('filament-resources/contract.fields.end_date'))
                    ->required()
                    ->default(fn (MaintenanceRequest $record): Carbon =>
                        now()->addMonths($record->contractType->period ?? 12)
                    ),
                Forms\Components\Select::make('status')
                    ->label(__('filament-resources/contract.fields.status'))
                    ->required()
                    ->options([
                        'pending' => __('filament-resources/contract.status_options.pending'),
                        'active' => __('filament-resources/contract.status_options.active'),
                    ])
                    ->default('active'),
                Forms\Components\Textarea::make('terms')
                    ->label(__('filament-resources/contract.fields.terms'))
                    ->rows(3)
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('notes')
                    ->label(__('filament-resources/contract.fields.notes'))
                    ->rows(3)
                    ->columnSpanFull(),
            ])
            ->action(function (MaintenanceRequest $record, array $data): void {
                try {
                    // Create a new contract
                    $contract = new Contract();
                    $contract->contract_number = $data['contract_number'];
                    $contract->client_id = $record->client_id;
                    $contract->contract_type_id = $record->contract_type_id;
                    $contract->visits_included = $record->contractType->visit_limit ?? 0;
                    $contract->maintenance_request_id = $record->id;
                    $contract->start_date = $data['start_date'];
                    $contract->end_date = $data['end_date'];
                    $contract->status = $data['status'];
                    $contract->terms = $data['terms'] ?? null;
                    $contract->notes = $data['notes'] ?? null;
                    $contract->save();

                    // Update the maintenance request
                    $record->update([
                        'contract_id' => $contract->id,
                        'status' => $data['status'] === 'active' ? 'completed' : $record->status,
                        'completed_at' => $data['status'] === 'active' ? now() : $record->completed_at,
                    ]);

                    Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.contract_created'))
                        ->body(__('filament-resources/maintenance-request.notifications.contract_created_body', ['number' => $data['contract_number']]))
                        ->success()
                        ->send();

                } catch (\Exception $e) {
                    Log::error('Error creating contract: ' . $e->getMessage());

                    Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.contract_creation_failed'))
                        ->body(__('filament-resources/maintenance-request.notifications.contract_creation_failed_body'))
                        ->danger()
                        ->send();
                }
            });
    }

    /**
     * Get the delete action for table rows.
     *
     * @return Tables\Actions\DeleteAction
     */
    protected static function getDeleteAction(): Tables\Actions\DeleteAction
    {
        return Tables\Actions\DeleteAction::make()
            ->label(__('filament-resources/maintenance-request.actions.delete'))
            ->visible(fn (MaintenanceRequest $record): bool =>
                Auth::user()->can('delete', $record)
            )
            ->requiresConfirmation()
            ->modalHeading(__('filament-resources/maintenance-request.actions.delete_modal_heading'))
            ->modalDescription(__('filament-resources/maintenance-request.actions.delete_modal_description'))
            ->modalSubmitActionLabel(__('filament-resources/maintenance-request.actions.delete_confirm'));
    }

    /**
     * Get the relations for the resource.
     *
     * @return array<string>
     */
    public static function getRelations(): array
    {
        return [
            RelationManagers\PaymentsRelationManager::class,
            RelationManagers\TechnicianReportsRelationManager::class,
            // Uncomment when ready to use
            // RelationManagers\VisitsRelationManager::class,
            // RelationManagers\DocumentsRelationManager::class,
        ];
    }

    /**
     * Get the pages for the resource.
     *
     * @return array<string, \Filament\Resources\Pages\PageRegistration>
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaintenanceRequests::route('/'),
            'create' => Pages\CreateMaintenanceRequest::route('/create'),
            'view' => Pages\ViewMaintenanceRequest::route('/{record}'),
            'edit' => Pages\EditMaintenanceRequest::route('/{record}/edit'),
        ];
    }

    /**
     * Get the Eloquent query for the resource with optimizations.
     *
     * @return Builder
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'client:id,name,phone,email',
                'contract:id,maintenance_request_id,contract_number,maintenance_request_id',
                'contractType:id,name,period,visit_limit',
                'assignedTechnician:id,name',
            ])
            ->withCount(['payments'])
            ->latest('created_at');
    }

    /**
     * Get the global search results for the resource.
     *
     * @return array<string>
     */
    public static function getGlobalSearchResultTitle(Model $record): string
    {
        return $record->request_number . ' - ' . ($record->title ?? __('filament-resources/maintenance-request.untitled'));
    }

    /**
     * Get the global search result details.
     *
     * @param Model $record
     * @return array<string>
     */
    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            __('filament-resources/maintenance-request.global_search.client') => $record->client?->name,
            __('filament-resources/maintenance-request.global_search.status') => __("filament-resources/maintenance-request.status_options.{$record->status}"),
            __('filament-resources/maintenance-request.global_search.created') => $record->created_at?->diffForHumans(),
        ];
    }

    /**
     * Get the global search result actions.
     *
     * @param Model $record
     * @return array<\Filament\GlobalSearch\Actions\Action>
     */
    public static function getGlobalSearchResultActions(Model $record): array
    {
        return [
            Action::make('view')
                ->label(__('filament-resources/maintenance-request.actions.view'))
                ->url(static::getUrl('view', ['record' => $record]))
                ->icon('heroicon-o-eye'),
            Action::make('edit')
                ->label(__('filament-resources/maintenance-request.actions.edit'))
                ->url(static::getUrl('edit', ['record' => $record]))
                ->icon('heroicon-o-pencil')
                ->visible(fn (): bool => Auth::user()->can('update', $record)),
        ];
    }

    /**
     * Clear all resource-related caches.
     *
     * @return void
     */
    public static function clearAllCaches(): void
    {
        static::clearFormCaches();
        static::clearTableCaches();

        // Clear navigation badge cache
        $cacheKey = 'maintenance_requests_pending_count_' . Auth::id();
        Cache::forget($cacheKey);
    }
}
