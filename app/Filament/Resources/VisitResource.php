<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VisitResource\Pages;
use App\Filament\Resources\VisitResource\RelationManagers;
use App\Models\Visit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class VisitResource extends Resource
{
    protected static ?string $model = Visit::class;
    //protected static ?string $navigationIcon = 'heroicon-o-truck';

    public static function getNavigationGroup(): string
    {
        return __('navigation.groups.maintenance_management');
    }

    protected static ?int $navigationSort = 2;

    public static function getModelLabel(): string
    {
        return __('navigation.resources.visit.label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('navigation.resources.visit.plural');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Card::make()
                    ->schema([
                        Forms\Components\Select::make('contract_id')
                            ->label(__('filament-resources/visit.fields.contract_id'))
                            ->relationship('contract', 'contract_number', function ($query) {
                                return $query->with('client')->orderBy('contract_number');
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->getOptionLabelFromRecordUsing(fn ($record) =>
                                $record->contract_number . ' - ' . ($record->client->name ?? __('filament-resources/visit.placeholders.no_client'))
                            ),
                        Forms\Components\Select::make('technician_id')
                            ->label(__('filament-resources/visit.fields.technician_id'))
                            ->relationship('technician', 'name', function ($query) {
                                return $query/*->where('role', 'technician')*/;
                            })
                            ->required()
                            ->searchable()
                            ->preload(),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('scheduled_date')
                                    ->label(__('filament-resources/visit.fields.scheduled_date'))
                                    ->required()
                                    ->reactive()
                                    ->rules([
                                        'required_with:scheduled_time',
                                    ])
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        if ($state && $get('scheduled_time')) {
                                            $datetime = \Carbon\Carbon::createFromFormat('Y-m-d', $state)
                                                ->setTimeFromTimeString($get('scheduled_time'))
                                                ->format('Y-m-d H:i:s');
                                            $set('scheduled_at', $datetime);
                                        }
                                    }),
                                Forms\Components\TimePicker::make('scheduled_time')
                                    ->label(__('filament-resources/visit.fields.scheduled_time'))
                                    ->required()
                                    ->reactive()
                                    ->rules([
                                        'required_with:scheduled_date',
                                    ])
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        if ($state && $get('scheduled_date')) {
                                            $datetime = \Carbon\Carbon::createFromFormat('Y-m-d', $get('scheduled_date'))
                                                ->setTimeFromTimeString($state)
                                                ->format('Y-m-d H:i:s');
                                            $set('scheduled_at', $datetime);
                                        }
                                    }),
                            ]),
                        // Hidden fields to store the combined datetime values
                        Forms\Components\Hidden::make('scheduled_at'),
                        Forms\Components\Select::make('status')
                            ->label(__('filament-resources/visit.fields.status'))
                            ->required()
                            ->options([
                                'scheduled' => __('filament-resources/visit.status_options.scheduled'),
                                'in_progress' => __('filament-resources/visit.status_options.in_progress'),
                                'completed' => __('filament-resources/visit.status_options.completed'),
                                'canceled' => __('filament-resources/visit.status_options.canceled'),
                            ])
                            ->default('scheduled'),
                        Forms\Components\Textarea::make('findings')
                            ->label(__('filament-resources/visit.fields.findings'))
                            ->columnSpan('full'),
                        Forms\Components\Textarea::make('actions_taken')
                            ->label(__('filament-resources/visit.fields.actions_taken'))
                            ->columnSpan('full'),
                        Forms\Components\Textarea::make('recommendations')
                            ->label(__('filament-resources/visit.fields.recommendations'))
                            ->columnSpan('full'),
                        Forms\Components\Textarea::make('notes')
                            ->label(__('filament-resources/visit.fields.notes'))
                            ->columnSpan('full'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['contract.client', 'technician']))
            ->columns([
                Tables\Columns\TextColumn::make('contract.contract_number')
                    ->label(__('filament-resources/visit.columns.contract.contract_number'))
                    ->searchable()
                    ->sortable()
                    ->placeholder(__('filament-resources/visit.placeholders.no_contract')),
                Tables\Columns\TextColumn::make('contract.client.name')
                    ->label(__('filament-resources/visit.columns.client_name'))
                    ->searchable()
                    ->sortable()
                    ->placeholder(__('filament-resources/visit.placeholders.no_client')),
                Tables\Columns\TextColumn::make('technician.name')
                    ->label(__('filament-resources/visit.columns.technician.name'))
                    ->searchable()
                    ->sortable()
                    ->placeholder(__('filament-resources/visit.placeholders.no_technician')),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->label(__('filament-resources/visit.columns.scheduled_at'))
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('filament-resources/visit.columns.status'))
                    ->colors([
                        'primary' => 'scheduled',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'danger' => 'canceled',
                    ]),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('contract_id')
                    ->label(__('filament-resources/visit.filters.contract_id'))
                    ->relationship('contract', 'contract_number'),
                Tables\Filters\SelectFilter::make('technician_id')
                    ->label(__('filament-resources/visit.filters.technician_id'))
                    ->relationship('technician', 'name'),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('filament-resources/visit.filters.status'))
                    ->options([
                        'scheduled' => __('filament-resources/visit.status_options.scheduled'),
                        'in_progress' => __('filament-resources/visit.status_options.in_progress'),
                        'completed' => __('filament-resources/visit.status_options.completed'),
                        'canceled' => __('filament-resources/visit.status_options.canceled'),
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(__('filament-resources/visit.actions.edit')),
                Tables\Actions\DeleteAction::make()
                    ->label(__('filament-resources/visit.actions.delete')),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //RelationManagers\DocumentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVisits::route('/'),
            'create' => Pages\CreateVisit::route('/create'),
            'edit' => Pages\EditVisit::route('/{record}/edit'),
        ];
    }

    /**
     * Helper method to combine date and time into datetime
     */
    protected function updateDateTime($date, $time, callable $set, string $field): void
    {
        if ($date && $time) {
            $datetime = \Carbon\Carbon::createFromFormat('Y-m-d', $date)
                ->setTimeFromTimeString($time)
                ->format('Y-m-d H:i:s');
            $set($field, $datetime);
        } elseif ($date && !$time) {
            // If only date is set, set time to 00:00:00
            $datetime = \Carbon\Carbon::createFromFormat('Y-m-d', $date)
                ->startOfDay()
                ->format('Y-m-d H:i:s');
            $set($field, $datetime);
        } else {
            // Clear the datetime field if date is not set
            $set($field, null);
        }
    }

    /**
     * Mutate form data before filling the form (for editing)
     */
    public static function mutateFormDataBeforeFill(array $data): array
    {
        // Split datetime fields into separate date and time fields
        if (!empty($data['scheduled_at'])) {
            $scheduledAt = \Carbon\Carbon::parse($data['scheduled_at']);
            $data['scheduled_date'] = $scheduledAt->format('Y-m-d');
            $data['scheduled_time'] = $scheduledAt->format('H:i');
        }

        if (!empty($data['started_at'])) {
            $startedAt = \Carbon\Carbon::parse($data['started_at']);
            $data['started_date'] = $startedAt->format('Y-m-d');
            $data['started_time'] = $startedAt->format('H:i');
        }

        if (!empty($data['completed_at'])) {
            $completedAt = \Carbon\Carbon::parse($data['completed_at']);
            $data['completed_date'] = $completedAt->format('Y-m-d');
            $data['completed_time'] = $completedAt->format('H:i');
        }

        return $data;
    }

    /**
     * Mutate form data before saving
     */
    public static function mutateFormDataBeforeSave(array $data): array
    {
        // Combine date and time fields into datetime fields
        if (!empty($data['scheduled_date']) && !empty($data['scheduled_time'])) {
            $data['scheduled_at'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i',
                $data['scheduled_date'] . ' ' . $data['scheduled_time']
            )->format('Y-m-d H:i:s');
        } elseif (!empty($data['scheduled_date'])) {
            $data['scheduled_at'] = \Carbon\Carbon::createFromFormat('Y-m-d', $data['scheduled_date'])
                ->startOfDay()
                ->format('Y-m-d H:i:s');
        }

        if (!empty($data['started_date']) && !empty($data['started_time'])) {
            $data['started_at'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i',
                $data['started_date'] . ' ' . $data['started_time']
            )->format('Y-m-d H:i:s');
        } elseif (!empty($data['started_date'])) {
            $data['started_at'] = \Carbon\Carbon::createFromFormat('Y-m-d', $data['started_date'])
                ->startOfDay()
                ->format('Y-m-d H:i:s');
        }

        if (!empty($data['completed_date']) && !empty($data['completed_time'])) {
            $data['completed_at'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i',
                $data['completed_date'] . ' ' . $data['completed_time']
            )->format('Y-m-d H:i:s');
        } elseif (!empty($data['completed_date'])) {
            $data['completed_at'] = \Carbon\Carbon::createFromFormat('Y-m-d', $data['completed_date'])
                ->startOfDay()
                ->format('Y-m-d H:i:s');
        }

        // Remove the separate date and time fields as they're not database columns
        unset($data['scheduled_date'], $data['scheduled_time']);
        unset($data['started_date'], $data['started_time']);
        unset($data['completed_date'], $data['completed_time']);

        return $data;
    }
}
