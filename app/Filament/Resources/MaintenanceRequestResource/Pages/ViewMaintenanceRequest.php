<?php

declare(strict_types=1);

namespace App\Filament\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Resources\MaintenanceRequestResource;
use App\Filament\Traits\HasMaintenanceRequestHelpers;
use App\Models\MaintenanceRequest;
use App\Models\Payment;
use App\Models\Contract;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Card;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\Split;
use Filament\Actions\Action;
use Filament\Support\Enums\FontWeight;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

/**
 * ViewMaintenanceRequest Page
 *
 * Enhanced view page for maintenance requests with:
 * - Comprehensive information display with tabs
 * - Cached data for improved performance
 * - Enhanced user actions and workflows
 * - Accessibility improvements
 * - Real-time status updates
 *
 * @package App\Filament\Resources\MaintenanceRequestResource\Pages
 * <AUTHOR> Development Team
 * @version 2.0.0
 */
class ViewMaintenanceRequest extends ViewRecord
{
    use HasMaintenanceRequestHelpers;

    /**
     * The resource associated with the page.
     */
    protected static string $resource = MaintenanceRequestResource::class;



    /**
     * Get the page title.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.pages.view.title', [
            'number' => $this->getRecord()->request_number,
        ], 'عرض طلب الصيانة: ' . $this->getRecord()->request_number);
    }

    /**
     * Get the page heading.
     *
     * @return string
     */
    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.pages.view.heading', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Mount the page with optimized data loading.
     *
     * @param int|string $record
     * @return void
     */
    public function mount(int|string $record): void
    {
        parent::mount($record);

        // Eager load relationships for better performance
        $this->record->loadMissing([
            'client',
            'contract',
            'contractType',
            'assignedTechnician',
            'payments',
        ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make(3)
                    ->schema([
                        // Request Details column
                        Section::make(__('filament-resources/maintenance-request.sections.request_details'))
                            ->schema([
                                TextEntry::make('request_number')
                                    ->label(__('filament-resources/maintenance-request.fields.request_number'))
                                    ->weight(FontWeight::Bold)
                                    ->copyable()
                                    ->copyMessage(__('filament-resources/maintenance-request.messages.request_number_copied')),
                                TextEntry::make('title')
                                    ->label(__('filament-resources/maintenance-request.fields.title'))
                                    ->weight(FontWeight::Medium),

                                TextEntry::make('status')
                                    ->label(__('filament-resources/maintenance-request.fields.status'))
                                    ->badge()
                                    ->formatStateUsing(fn (string $state): string =>
                                        __("filament-resources/maintenance-request.status_options.{$state}")
                                    )
                                    ->color(fn (string $state): string => match ($state) {
                                        MaintenanceRequest::STATUS_NEW => 'warning',
                                        MaintenanceRequest::STATUS_ASSIGNED => 'secondary',
                                        MaintenanceRequest::STATUS_IN_PROGRESS => 'warning',
                                        'paid' => 'info',
                                        MaintenanceRequest::STATUS_COMPLETED => 'success',
                                        MaintenanceRequest::STATUS_CANCELLED => 'danger',
                                        default => 'gray',
                                    }),
                                TextEntry::make('created_at')
                                    ->label(__('filament-resources/maintenance-request.fields.created_at'))
                                    ->dateTime()
                                    ->since(),
                                TextEntry::make('completion_date')
                                    ->label(__('filament-resources/maintenance-request.fields.completion_date'))
                                    ->date()
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_set')),
                                TextEntry::make('completed_at')
                                    ->label(__('filament-resources/maintenance-request.fields.completed_at'))
                                    ->dateTime()
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_completed')),
                            ])
                            ->columnSpan(1),

                        // Client & Contract Information column
                        Section::make(__('filament-resources/maintenance-request.sections.client_contract'))
                            ->schema([
                                TextEntry::make('client.name')
                                    ->label(__('filament-resources/maintenance-request.fields.client_id'))
                                    ->weight(FontWeight::Bold)
                                    ->url(fn ($record) => $record->client_id
                                        ? MaintenanceRequestResource::getUrl('index', ['tableFilters' => ['client_id' => ['value' => $record->client_id]]])
                                        : null),
                                TextEntry::make('client.phone')
                                    ->label(__('filament-resources/maintenance-request.fields.client_phone'))
                                    ->icon('heroicon-o-phone')
                                    ->url(fn ($record) => $record->client?->phone ? "tel:{$record->client->phone}" : null),
                                TextEntry::make('contract.contract_number')
                                    ->label(__('filament-resources/maintenance-request.fields.contract_id'))
                                    ->url(fn ($record) => $record->contract_id
                                        ? url('/admin/contracts/' . $record->contract_id)
                                        : null)
                                    ->openUrlInNewTab()
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract')),
                                TextEntry::make('contractType.name')
                                    ->label(__('filament-resources/maintenance-request.fields.contract_type_id')),
                                TextEntry::make('visits_included')
                                    ->label(__('filament-resources/maintenance-request.fields.visits_included'))
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_set')),
                            ])
                            ->columnSpan(1),

                        // Assignment & Financials column
                        Section::make(__('filament-resources/maintenance-request.sections.assignment_financial'))
                            ->schema([
                                TextEntry::make('assignedTechnician.name')
                                    ->label(__('filament-resources/maintenance-request.fields.assigned_technician'))
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_assigned')),
                                TextEntry::make('price')
                                    ->label(__('filament-resources/maintenance-request.fields.price'))
                                    ->riyal()
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_priced'))
                                    ->color('success')
                                    ->weight(FontWeight::Bold),
                                TextEntry::make('payments_status')
                                    ->label(__('filament-resources/maintenance-request.fields.payment_status'))
                                    ->state(function (MaintenanceRequest $record): string {
                                        $payments = Payment::where('maintenance_request_id', $record->id)->get();
                                        $totalPaid = $payments->where('status', 'completed')->sum('amount');
                                        $totalAmount = $payments->sum('amount');

                                        if ($totalAmount == 0) return __('filament-resources/maintenance-request.payment_status.no_payments');
                                        if ($totalPaid == 0) return __('filament-resources/maintenance-request.payment_status.unpaid');
                                        if ($totalPaid < $totalAmount) return __('filament-resources/maintenance-request.payment_status.partially_paid');
                                        return __('filament-resources/maintenance-request.payment_status.fully_paid');
                                    })
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'لا توجد مدفوعات', 'No payments' => 'gray',
                                        'غير مدفوع', 'Unpaid' => 'danger',
                                        'مدفوع جزئياً', 'Partially paid' => 'warning',
                                        'مدفوع بالكامل', 'Fully paid' => 'success',
                                        default => 'gray',
                                    }),
                            ])
                            ->columnSpan(1),
                    ]),

                // Tabs section at the bottom
                Tabs::make(__('filament-resources/maintenance-request.sections.additional_info'))
                    ->columnSpanFull()
                    ->tabs([
                        Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.notes_details'))
                            ->schema([
                                TextEntry::make('notes')
                                    ->label(__('filament-resources/maintenance-request.fields.notes'))
                                    ->markdown()
                                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_notes_available'))
                                    ->columnSpanFull(),
                            ]),
                        Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.activity_timeline'))
                            ->schema([
                                // Placeholder for activity timeline
                                TextEntry::make('')
                                    ->state(__('filament-resources/maintenance-request.messages.activity_timeline_placeholder'))
                                    ->columnSpanFull(),
                            ]),
                        Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.financial_summary'))
                            ->schema([
                                Grid::make(3)
                                    ->schema([
                                        TextEntry::make('payments_total')
                                            ->label(__('filament-resources/maintenance-request.fields.total_amount_required'))
                                            ->state(function (MaintenanceRequest $record): int|string {
                                                return Payment::where('maintenance_request_id', $record->id)->sum('amount');
                                            })
                                            ->riyal(),
                                        TextEntry::make('payments_paid')
                                            ->label(__('filament-resources/maintenance-request.fields.amount_paid'))
                                            ->state(function (MaintenanceRequest $record): int|string {
                                                return Payment::where('maintenance_request_id', $record->id)
                                                    ->where('status', 'completed')
                                                    ->sum('amount');
                                            })
                                            ->riyal(),
                                        TextEntry::make('payments_pending')
                                            ->label(__('filament-resources/maintenance-request.fields.amount_pending'))
                                            ->state(function (MaintenanceRequest $record): int|string {
                                                return Payment::where('maintenance_request_id', $record->id)
                                                    ->where('status', 'pending')
                                                    ->sum('amount');
                                            })
                                            ->riyal(),
                                    ]),
                            ]),
                        Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.related_records'))
                            ->schema([
                                Section::make(__('filament-resources/maintenance-request.sections.contract_details'))
                                    ->schema([
                                        TextEntry::make('contract.contract_number')
                                            ->label(__('filament-resources/maintenance-request.fields.contract_number'))
                                            ->url(fn ($record) => $record->contract_id
                                                ? url('/admin/contracts/' . $record->contract_id)
                                                : null)
                                            ->openUrlInNewTab()
                                            ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract_associated')),
                                        TextEntry::make('contract.start_date')
                                            ->label(__('filament-resources/maintenance-request.fields.start_date'))
                                            ->date()
                                            ->placeholder(__('filament-resources/maintenance-request.placeholders.not_available')),
                                        TextEntry::make('contract.end_date')
                                            ->label(__('filament-resources/maintenance-request.fields.end_date'))
                                            ->date()
                                            ->placeholder(__('filament-resources/maintenance-request.placeholders.not_available')),
                                        TextEntry::make('contract.status')
                                            ->label(__('filament-resources/maintenance-request.fields.contract_status'))
                                            ->badge()
                                            ->placeholder(__('filament-resources/maintenance-request.placeholders.not_available'))
                                            ->color(fn (string $state): string => match ($state) {
                                                'pending' => 'warning',
                                                'active' => 'success',
                                                'expired' => 'danger',
                                                'terminated' => 'danger',
                                                default => 'gray',
                                            }),
                                    ])
                                    ->columns(4),
                            ]),
                    ]),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('goToPayments')
                ->label(__('filament-resources/maintenance-request.actions.view_payments'))
                ->color('secondary')
                ->icon('heroicon-o-currency-dollar')
                ->url(fn (MaintenanceRequest $record): string => $this->getResource()::getUrl('edit', ['record' => $record]) . '#relation-manager-maintenance-request-payments-relation-manager'),

            Action::make('assign_technician')
                ->label(__('filament-resources/maintenance-request.actions.assign_technician'))
                ->color('primary')
                ->icon('heroicon-o-user-plus')
                ->hidden(fn (MaintenanceRequest $record): bool => !empty($record->assigned_to))
                ->form([
                    Forms\Components\Select::make('assigned_to')
                        ->label(__('filament-resources/maintenance-request.fields.technician'))
                        ->relationship('assignedTechnician', 'name', function ($query) {
                            return $query->where('role', 'technician');
                        })
                        ->required()
                        ->preload()
                        ->searchable(),
                    Forms\Components\Textarea::make('assignment_notes')
                        ->label(__('filament-resources/maintenance-request.fields.assignment_notes'))
                        ->placeholder(__('filament-resources/maintenance-request.placeholders.add_technician_notes')),
                ])
                ->action(function (array $data, MaintenanceRequest $record): void {
                    $record->assigned_to = $data['assigned_to'];

                    // Update status to assigned if it was new
                    if ($record->status === MaintenanceRequest::STATUS_NEW) {
                        $record->status = MaintenanceRequest::STATUS_ASSIGNED;
                    }

                    $record->save();

                    // You could also notify the technician here

                    //$this->notify('success', 'Technician assigned successfully');
                })
                ->visible(fn (MaintenanceRequest $record): bool =>
                    in_array($record->status, [MaintenanceRequest::STATUS_NEW, MaintenanceRequest::STATUS_ASSIGNED]) &&
                    auth()->user()->can('update', $record)),

            Action::make('update_status')
                ->label(__('filament-resources/maintenance-request.actions.update_status'))
                ->color('warning')
                ->icon('heroicon-o-arrow-path')
                ->form([
                    Forms\Components\Select::make('status')
                        ->label(__('filament-resources/maintenance-request.fields.status'))
                        ->options([
                            MaintenanceRequest::STATUS_NEW => __('filament-resources/maintenance-request.status_options.new'),
                            MaintenanceRequest::STATUS_ASSIGNED => __('filament-resources/maintenance-request.status_options.assigned'),
                            MaintenanceRequest::STATUS_IN_PROGRESS => __('filament-resources/maintenance-request.status_options.in_progress'),
                            MaintenanceRequest::STATUS_COMPLETED => __('filament-resources/maintenance-request.status_options.completed'),
                            MaintenanceRequest::STATUS_CANCELLED => __('filament-resources/maintenance-request.status_options.canceled'),
                        ])
                        ->required(),
                    Forms\Components\Textarea::make('status_notes')
                        ->label(__('filament-resources/maintenance-request.fields.status_update_notes'))
                        ->placeholder(__('filament-resources/maintenance-request.placeholders.add_status_notes')),
                ])
                ->action(function (array $data, MaintenanceRequest $record): void {
                    $record->status = $data['status'];

                    if ($data['status'] === MaintenanceRequest::STATUS_COMPLETED && !$record->completed_at) {
                        $record->completed_at = now();
                    }

                    $record->save();

                    //$this->notify('success', 'Status updated successfully');
                })
                ->visible(fn (MaintenanceRequest $record): bool =>
                auth()->user()->can('update', $record)),

            // Add price/payment action if user has permission
            Action::make('add_price')
                ->label(__('filament-resources/maintenance-request.actions.set_price'))
                ->color('success')
                ->icon('heroicon-o-currency-dollar')
                ->form([
                    Forms\Components\TextInput::make('price')
                        ->label(__('filament-resources/maintenance-request.fields.price'))
                        ->required()
                        ->numeric()
                        ->prefix(__('filament-resources/maintenance-request.units.riyal_symbol')),
                    Forms\Components\Textarea::make('price_notes')
                        ->label(__('filament-resources/maintenance-request.fields.price_notes'))
                        ->placeholder(__('filament-resources/maintenance-request.placeholders.add_pricing_notes')),
                ])
                ->action(function (array $data, MaintenanceRequest $record): void {
                    // Update the maintenance request price
                    $record->price = $data['price'];
                    $record->save();

                    // Create a payment record
                    $payment = new Payment();
                    $payment->maintenance_request_id = $record->id;
                    $payment->amount = $data['price'];
                    $payment->due_date = now()->addDays(30);
                    $payment->status = 'pending';
                    $payment->notes = $data['price_notes'] ?? null;

                    $latestPayment = Payment::latest()->first();
                    $nextId = $latestPayment ? $latestPayment->id + 1 : 1;
                    $payment->payment_number = 'PAY-' . date('Ym') . '-' . str_pad("$nextId", 4, '0', STR_PAD_LEFT);

                    $payment->save();

                    //$this->notify('success', 'Price set and payment record created');
                })
                ->visible(fn (MaintenanceRequest $record): bool =>
                    is_null($record->price) &&
                    auth()->user()->can('update-price', $record)),

            Action::make('create_contract')
                ->label(__('filament-resources/maintenance-request.actions.create_contract'))
                ->color('primary')
                ->icon('heroicon-o-document-text')
                ->form([
                    Forms\Components\TextInput::make('contract_number')
                        ->label(__('filament-resources/maintenance-request.fields.contract_number'))
                        ->required()
                        ->maxLength(255)
                        ->unique('contracts', 'contract_number')
                        ->default(function () {
                            $latestContract = \App\Models\Contract::latest()->first();
                            $nextId = $latestContract ? $latestContract->id + 1 : 1;
                            return 'CNT-' . date('Ym') . '-' . str_pad("$nextId", 4, '0', STR_PAD_LEFT);
                        }),
                    Forms\Components\DatePicker::make('start_date')
                        ->label(__('filament-resources/maintenance-request.fields.start_date'))
                        ->required()
                        ->live()
                        ->afterStateUpdated(function (callable $set, $state, MaintenanceRequest $record) {
                            $set('end_date', \Carbon\Carbon::parse($state)->addMonths($record->contract_type->period));
                        })
                        ->default(now()),
                    Forms\Components\DatePicker::make('end_date')
                        ->label(__('filament-resources/maintenance-request.fields.end_date'))
                        ->required()
                        ->readOnly()
                        ->default(fn($record) => now()->addMonths($record->contract_type->period)),
                    Forms\Components\Select::make('status')
                        ->label(__('filament-resources/maintenance-request.fields.contract_status'))
                        ->required()
                        ->options([
                            'pending' => __('filament-resources/maintenance-request.contract_status.pending'),
                            'active' => __('filament-resources/maintenance-request.contract_status.active'),
                        ])
                        ->default('active'),
                    Forms\Components\Textarea::make('terms')
                        ->label(__('filament-resources/maintenance-request.fields.contract_terms'))
                        ->columnSpan('full'),
                    Forms\Components\Textarea::make('notes')
                        ->label(__('filament-resources/maintenance-request.fields.notes'))
                        ->columnSpan('full'),
                ])
                ->action(function (array $data, MaintenanceRequest $record): void {
                    // Create a new contract
                    $contract = new \App\Models\Contract();
                    $contract->contract_number = $data['contract_number'];
                    $contract->client_id = $record->client_id;
                    $contract->contract_type_id = $record->contract_type_id;
                    $contract->visits_included = $record->visits_included;
                    $contract->maintenance_request_id = $record->id;
                    $contract->start_date = $data['start_date'];
                    $contract->end_date = $data['end_date'];
                    $contract->status = $data['status'];
                    $contract->terms = $data['terms'] ?? null;
                    $contract->notes = $data['notes'] ?? null;
                    $contract->save();

                    // Update the maintenance request with the new contract_id
                    $record->contract_id = $contract->id;

                    // Update the maintenance request status to 'completed' if contract is active
                    if ($data['status'] === 'active') {
                        $record->status = MaintenanceRequest::STATUS_COMPLETED;
                        $record->completed_at = now();
                    }

                    $record->save();

                    //$this->notify('success', 'Contract created successfully');
                })
                ->visible(fn (MaintenanceRequest $record): bool =>
                    $record->status === 'paid' &&
                    !$record->contract_id &&
                    auth()->user()->can('create', \App\Models\Contract::class)),

            Action::make('print')
                ->label(__('filament-resources/maintenance-request.actions.print_request'))
                ->color('gray')
                ->icon('heroicon-o-printer')
                ->url(fn (MaintenanceRequest $record): string =>
                    route('maintenance-request.print', $record))
                ->openUrlInNewTab()
                ->visible(fn (MaintenanceRequest $record): bool =>
                    auth()->user()->can('view', $record)),
        ];
    }
}
