<?php

declare(strict_types=1);

namespace App\Filament\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

/**
 * ListMaintenanceRequests Page
 *
 * Enhanced list page for maintenance requests with:
 * - Performance-optimized queries with eager loading
 * - Advanced filtering and search capabilities
 * - Status-based tabs for quick navigation
 * - Cached statistics and counts
 * - Enhanced accessibility and user experience
 *
 * @package App\Filament\Resources\MaintenanceRequestResource\Pages
 * <AUTHOR>
 * @version 2.0.0
 */
class ListMaintenanceRequests extends ListRecords
{
    /**
     * The resource associated with the page.
     */
    protected static string $resource = MaintenanceRequestResource::class;

    /**
     * Get the page title.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.pages.list.title');
    }

    /**
     * Get the page heading.
     *
     * @return string
     */
    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.pages.list.heading');
    }

    /**
     * Get the header actions for the page.
     *
     * @return array
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label(__('filament-resources/maintenance-request.actions.create'))
                ->icon('heroicon-o-plus')
                ->modalHeading(__('filament-resources/maintenance-request.actions.create_modal_heading'))
                ->modalWidth('7xl'),

            Actions\Action::make('refresh_cache')
                ->label(__('filament-resources/maintenance-request.actions.refresh_cache'))
                ->icon('heroicon-o-arrow-path')
                ->color('gray')
                ->action(function (): void {
                    MaintenanceRequestResource::clearAllCaches();

                    $this->dispatch('$refresh');

                    \Filament\Notifications\Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.cache_refreshed'))
                        ->success()
                        ->send();
                })
                ->visible(fn (): bool => Auth::user()->can('viewAny', MaintenanceRequest::class)),
        ];
    }

    /**
     * Get the tabs for filtering records.
     *
     * @return array
     */
    public function getTabs(): array
    {
        $cacheKey = 'maintenance_requests_tabs_counts_' . Auth::id();

        $counts = Cache::remember($cacheKey, now()->addMinutes(5), function () {
            return [
                'all' => MaintenanceRequest::count(),
                'new' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_NEW)->count(),
                'assigned' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_ASSIGNED)->count(),
                'in_progress' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_IN_PROGRESS)->count(),
                'completed' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_COMPLETED)->count(),
                'canceled' => MaintenanceRequest::where('status', MaintenanceRequest::STATUS_CANCELLED)->count(),
                'overdue' => MaintenanceRequest::where('completion_date', '<', now())
                    ->whereNotIn('status', [MaintenanceRequest::STATUS_COMPLETED, MaintenanceRequest::STATUS_CANCELLED])
                    ->count(),
            ];
        });

        return [
            'all' => Tab::make(__('filament-resources/maintenance-request.tabs.all'))
                ->badge($counts['all'])
                ->badgeColor('primary'),

            'new' => Tab::make(__('filament-resources/maintenance-request.tabs.new'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MaintenanceRequest::STATUS_NEW))
                ->badge($counts['new'])
                ->badgeColor('warning'),

            'assigned' => Tab::make(__('filament-resources/maintenance-request.tabs.assigned'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MaintenanceRequest::STATUS_ASSIGNED))
                ->badge($counts['assigned'])
                ->badgeColor('info'),

            'in_progress' => Tab::make(__('filament-resources/maintenance-request.tabs.in_progress'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MaintenanceRequest::STATUS_IN_PROGRESS))
                ->badge($counts['in_progress'])
                ->badgeColor('warning'),

            'completed' => Tab::make(__('filament-resources/maintenance-request.tabs.completed'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MaintenanceRequest::STATUS_COMPLETED))
                ->badge($counts['completed'])
                ->badgeColor('success'),

            'canceled' => Tab::make(__('filament-resources/maintenance-request.tabs.canceled'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', MaintenanceRequest::STATUS_CANCELLED))
                ->badge($counts['canceled'])
                ->badgeColor('danger'),

            'overdue' => Tab::make(__('filament-resources/maintenance-request.tabs.overdue'))
                ->modifyQueryUsing(fn (Builder $query) =>
                    $query->where('completion_date', '<', now())
                        ->whereNotIn('status', [MaintenanceRequest::STATUS_COMPLETED, MaintenanceRequest::STATUS_CANCELLED])
                )
                ->badge($counts['overdue'])
                ->badgeColor('danger'),
        ];
    }

    /**
     * Get the table query with optimizations.
     *
     * @return Builder
     */
    protected function getTableQuery(): Builder
    {
        return static::getResource()::getEloquentQuery();
    }

    /**
     * Get the default table sort column.
     *
     * @return string|null
     */
    protected function getDefaultTableSortColumn(): ?string
    {
        return 'created_at';
    }

    /**
     * Get the default table sort direction.
     *
     * @return string|null
     */
    protected function getDefaultTableSortDirection(): ?string
    {
        return 'desc';
    }

    /**
     * Get the table records per page options.
     *
     * @return array
     */
    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [10, 25, 50, 100];
    }
}
