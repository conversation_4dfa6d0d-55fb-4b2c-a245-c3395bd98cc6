<?php

declare(strict_types=1);

namespace App\Filament\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

/**
 * EditMaintenanceRequest Page
 *
 * Enhanced edit page for maintenance requests with:
 * - Comprehensive validation and error handling
 * - Optimized data loading with caching
 * - Enhanced user feedback and notifications
 * - Accessibility improvements
 * - Audit trail logging
 *
 * @package App\Filament\Resources\MaintenanceRequestResource\Pages
 * <AUTHOR>
 * @version 2.0.0
 */
class EditMaintenanceRequest extends EditRecord
{
    /**
     * The resource associated with the page.
     */
    protected static string $resource = MaintenanceRequestResource::class;

    /**
     * Get the page title.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.pages.edit.title', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Get the page heading.
     *
     * @return string
     */
    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.pages.edit.heading', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Get the page subheading.
     *
     * @return string|null
     */
    public function getSubheading(): ?string
    {
        $record = $this->getRecord();
        return __('filament-resources/maintenance-request.pages.edit.subheading', [
            'client' => $record->client?->name,
            'status' => __("filament-resources/maintenance-request.status_options.{$record->status}"),
        ]);
    }

    /**
     * Mutate form data before filling the form.
     *
     * @param array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Ensure dates are properly formatted
        if (isset($data['request_date'])) {
            $data['request_date'] = $data['request_date']?->format('Y-m-d');
        }

        if (isset($data['completion_date'])) {
            $data['completion_date'] = $data['completion_date']?->format('Y-m-d');
        }

        return $data;
    }

    /**
     * Mutate form data before saving the record.
     *
     * @param array $data
     * @return array
     */
    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Track status changes
        $originalStatus = $this->getRecord()->status;
        if (isset($data['status']) && $data['status'] !== $originalStatus) {
            $data['status_changed_at'] = now();
            $data['status_changed_by'] = Auth::id();

            // Set completion date if status is completed
            if ($data['status'] === MaintenanceRequest::STATUS_COMPLETED && !$this->getRecord()->completed_at) {
                $data['completed_at'] = now();
            }
        }

        return $data;
    }

    /**
     * Handle record update with enhanced error handling.
     *
     * @param array $data
     * @return Model
     */
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $originalData = $record->toArray();

        try {
            $updatedRecord = parent::handleRecordUpdate($record, $data);

            // Log the changes
            $this->logChanges($originalData, $data);

            // Clear relevant caches
            $this->clearCaches();

            // Send success notification
            $this->sendSuccessNotification($updatedRecord);

            return $updatedRecord;

        } catch (\Exception $e) {
            Log::error('Error updating maintenance request: ' . $e->getMessage(), [
                'record_id' => $record->id,
                'data' => $data,
                'user_id' => Auth::id(),
            ]);

            Notification::make()
                ->title(__('filament-resources/maintenance-request.notifications.update_failed'))
                ->body(__('filament-resources/maintenance-request.notifications.update_failed_body'))
                ->danger()
                ->persistent()
                ->send();

            throw $e;
        }
    }

    /**
     * Log changes made to the record.
     *
     * @param array $originalData
     * @param array $newData
     * @return void
     */
    protected function logChanges(array $originalData, array $newData): void
    {
        $changes = [];

        foreach ($newData as $key => $value) {
            if (isset($originalData[$key]) && $originalData[$key] !== $value) {
                $changes[$key] = [
                    'old' => $originalData[$key],
                    'new' => $value,
                ];
            }
        }

        if (!empty($changes)) {
            Log::info('Maintenance request updated', [
                'record_id' => $this->getRecord()->id,
                'changes' => $changes,
                'user_id' => Auth::id(),
            ]);
        }
    }

    /**
     * Send success notification after record update.
     *
     * @param MaintenanceRequest $record
     * @return void
     */
    protected function sendSuccessNotification(MaintenanceRequest $record): void
    {
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.updated_successfully'))
            ->body(__('filament-resources/maintenance-request.notifications.updated_successfully_body', [
                'number' => $record->request_number,
            ]))
            ->success()
            ->send();
    }

    /**
     * Clear relevant caches after record update.
     *
     * @return void
     */
    protected function clearCaches(): void
    {
        // Clear navigation badge cache
        $cacheKey = 'maintenance_requests_pending_count_' . Auth::id();
        Cache::forget($cacheKey);

        // Clear table caches
        MaintenanceRequestResource::clearTableCaches();
    }

    /**
     * Get the header actions for the page.
     *
     * @return array
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label(__('filament-resources/maintenance-request.actions.view'))
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->label(__('filament-resources/maintenance-request.actions.delete'))
                ->icon('heroicon-o-trash')
                ->requiresConfirmation()
                ->modalHeading(__('filament-resources/maintenance-request.actions.delete_modal_heading'))
                ->modalDescription(__('filament-resources/maintenance-request.actions.delete_modal_description'))
                ->visible(fn (): bool => Auth::user()->can('delete', $this->getRecord())),
        ];
    }

    /**
     * Get the form actions.
     *
     * @return array
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction()
                ->label(__('filament-resources/maintenance-request.actions.save'))
                ->icon('heroicon-o-check'),
            $this->getCancelFormAction()
                ->label(__('filament-resources/maintenance-request.actions.cancel'))
                ->icon('heroicon-o-x-mark'),
        ];
    }
}
