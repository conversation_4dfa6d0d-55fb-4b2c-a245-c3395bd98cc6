<?php

declare(strict_types=1);

namespace App\Filament\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Resources\MaintenanceRequestResource;
use App\Models\MaintenanceRequest;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;

/**
 * CreateMaintenanceRequest Page
 *
 * Enhanced create page for maintenance requests with:
 * - Improved form validation and error handling
 * - Auto-population of default values
 * - Cache invalidation after creation
 * - Enhanced user feedback and notifications
 * - Accessibility improvements
 *
 * @package App\Filament\Resources\MaintenanceRequestResource\Pages
 * <AUTHOR>
 * @version 2.0.0
 */
class CreateMaintenanceRequest extends CreateRecord
{
    /**
     * The resource associated with the page.
     */
    protected static string $resource = MaintenanceRequestResource::class;

    /**
     * The page title.
     */
    protected static ?string $title = null;

    /**
     * Get the page title.
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.pages.create.title');
    }

    /**
     * Get the page heading.
     *
     * @return string
     */
    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.pages.create.heading');
    }

    /**
     * Get the page subheading.
     *
     * @return string|null
     */
    public function getSubheading(): ?string
    {
        return __('filament-resources/maintenance-request.pages.create.subheading');
    }

    /**
     * Mutate form data before creating the record.
     *
     * @param array $data
     * @return array
     */
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set default values
        $data['request_date'] = $data['request_date'] ?? now();
        $data['status'] = $data['status'] ?? MaintenanceRequest::STATUS_NEW;

        // Set the creator
        if (Auth::check()) {
            $data['created_by_id'] = Auth::id();
            $data['created_by_type'] = get_class(Auth::user());
        }

        return $data;
    }

    /**
     * Handle record creation with enhanced error handling.
     *
     * @param array $data
     * @return Model
     */
    protected function handleRecordCreation(array $data): Model
    {
        try {
            $record = parent::handleRecordCreation($data);

            // Clear relevant caches
            $this->clearCaches();

            // Send success notification
            $this->sendSuccessNotification($record);

            return $record;

        } catch (\Exception $e) {
            Log::error('Error creating maintenance request: ' . $e->getMessage(), [
                'data' => $data,
                'user_id' => Auth::id(),
            ]);

            Notification::make()
                ->title(__('filament-resources/maintenance-request.notifications.creation_failed'))
                ->body(__('filament-resources/maintenance-request.notifications.creation_failed_body'))
                ->danger()
                ->persistent()
                ->send();

            throw $e;
        }
    }

    /**
     * Send success notification after record creation.
     *
     * @param MaintenanceRequest $record
     * @return void
     */
    protected function sendSuccessNotification(MaintenanceRequest $record): void
    {
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.created_successfully'))
            ->body(__('filament-resources/maintenance-request.notifications.created_successfully_body', [
                'number' => $record->request_number,
            ]))
            ->success()
            ->actions([
                \Filament\Notifications\Actions\Action::make('view')
                    ->label(__('filament-resources/maintenance-request.actions.view'))
                    ->url(MaintenanceRequestResource::getUrl('view', ['record' => $record]))
                    ->button(),
            ])
            ->send();
    }

    /**
     * Clear relevant caches after record creation.
     *
     * @return void
     */
    protected function clearCaches(): void
    {
        // Clear navigation badge cache
        $cacheKey = 'maintenance_requests_pending_count_' . Auth::id();
        Cache::forget($cacheKey);

        // Clear form caches
        MaintenanceRequestResource::clearFormCaches();
    }

    /**
     * Get the form actions.
     *
     * @return array
     */
    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction()
                ->label(__('filament-resources/maintenance-request.actions.create'))
                ->icon('heroicon-o-plus'),
            $this->getCreateAnotherFormAction()
                ->label(__('filament-resources/maintenance-request.actions.create_another'))
                ->icon('heroicon-o-plus-circle'),
            $this->getCancelFormAction()
                ->label(__('filament-resources/maintenance-request.actions.cancel'))
                ->icon('heroicon-o-x-mark'),
        ];
    }

    /**
     * Get the redirect URL after record creation.
     *
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }
}
