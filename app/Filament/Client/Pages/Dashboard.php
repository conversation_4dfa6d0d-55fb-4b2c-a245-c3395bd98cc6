<?php

namespace App\Filament\Client\Pages;

use App\Models\MaintenanceRequest;
use App\Models\Visit;
use App\Models\Payment;
use Carbon\Carbon;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class Dashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    protected static string $view = 'filament.client.pages.dashboard';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationLabel = null;
    protected static ?string $title = null;

    public static function getNavigationLabel(): string
    {
        return __('client.navigation.dashboard');
    }

    public function getTitle(): string
    {
        return __('client.dashboard.title');
    }
    protected static ?string $slug = 'dashboard';

    public $currentTime;
    public $greeting;

    public function mount(): void
    {
        $this->currentTime = now();
        $this->greeting = $this->getGreeting();
    }

    protected function getGreeting(): string
    {
        $hour = $this->currentTime->format('H');
        return $hour < 12 ? __('client.dashboard.good_morning') : __('client.dashboard.good_evening');
    }

    public function getStats(): array
    {
        $clientId = Auth::guard('client')->id();

        $contractStats = MaintenanceRequest::where('client_id', $clientId)
            ->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->pluck('total', 'status')
            ->toArray();

        return [
            'active_contracts' => $contractStats['approved'] ?? 0,
            'pending_contracts' => ($contractStats['new'] ?? 0) + ($contractStats['pending'] ?? 0),
            'completed_contracts' => $contractStats['completed'] ?? 0,
            'total_contracts' => MaintenanceRequest::where('client_id', $clientId)->count(),
            'upcoming_visits' => $this->getUpcomingVisitsCount(),
        ];
    }

    public function getUpcomingVisitsCount(): int
    {
        $clientId = Auth::guard('client')->id();
        return Visit::whereHas('contract.maintenanceRequest', function($query) use ($clientId) {
            $query->where('client_id', $clientId);
        })
            ->whereIn('status', ['scheduled', 'in_progress'])
            ->where('scheduled_at', '>=', now())
            ->count();
    }

    public function getUpcomingVisits(): array
    {
        $clientId = Auth::guard('client')->id();

        return Visit::with(['contract.maintenanceRequest', 'technician'])
            ->whereHas('contract.maintenanceRequest', function($query) use ($clientId) {
                $query->where('client_id', $clientId);
            })
            ->whereIn('status', ['scheduled', 'in_progress'])
            ->where('scheduled_at', '>=', now())
            ->orderBy('scheduled_at', 'asc')
            ->take(2)
            ->get()
            ->map(function($visit) {
                return [
                    'contract_number' => $visit->contract->maintenanceRequest->request_number,
                    'visit_date' => $visit->scheduled_at->format('d/m/Y'),
                    'visit_time' => $visit->scheduled_at->format('h:i A'),
                    'technician' => $visit->technician?->name ?? __('client.dashboard.not_assigned'),
                    'status_label' => $visit->status === 'scheduled' ? __('client.dashboard.visit_status.scheduled') : __('client.dashboard.visit_status.in_progress'),
                ];
            })->toArray();
    }

    public function getRecentContracts()
    {
        $clientId = Auth::guard('client')->id();
        return MaintenanceRequest::with(['contract_type', 'contract'])
            ->where('client_id', $clientId)
            ->latest()
            ->take(3)
            ->get();
    }

    public function getNotifications(): array
    {
        return [
            [
                'type' => 'info',
                'title' => __('client.dashboard.notifications.upcoming_visit_title'),
                'message' => __('client.dashboard.notifications.upcoming_visit_message'),
                'time' => __('client.dashboard.notifications.time_hours', ['hours' => 2]),
            ],
            [
                'type' => 'warning',
                'title' => __('client.dashboard.notifications.certificate_expiring_title'),
                'message' => __('client.dashboard.notifications.certificate_expiring_message'),
                'time' => __('client.dashboard.notifications.time_day'),
            ],
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            /*Action::make('emergency_request')
                ->label('صيانة طارئة')
                ->icon('heroicon-o-exclamation-triangle')
                ->color('danger')
                ->form([
                    Forms\Components\Select::make('urgency')
                        ->label('مستوى الأولوية')
                        ->options([
                            'high' => 'عالية',
                            'critical' => 'حرجة',
                        ])
                        ->required(),

                    Forms\Components\Textarea::make('description')
                        ->label('وصف المشكلة')
                        ->required(),
                ])
                ->action(function (array $data): void {
                    Notification::make()
                        ->title('تم إرسال طلب الصيانة الطارئة')
                        ->body('سيتم التواصل معك خلال 15 دقيقة')
                        ->success()
                        ->send();
                }),*/

            Action::make('new_contract')
                ->label(__('client.dashboard.actions.new_contract'))
                ->icon('heroicon-o-plus-circle')
                ->color('primary')
                ->url(route('filament.client.resources.maintenance-contracts.wizard.step1')),
        ];
    }

    public static function canAccess(): bool
    {
        return true;
    }
}
