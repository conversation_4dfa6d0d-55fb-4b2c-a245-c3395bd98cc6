<?php

namespace App\Filament\Client\Resources;

use App\Models\ContractType;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Client\Resources\MaintenanceRequestResource\Pages;
use App\Models\MaintenanceRequest;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MaintenanceRequestResource extends Resource
{
    protected static ?string $model = MaintenanceRequest::class;

    protected static ?string $navigationIcon = 'heroicon-o-wrench-screwdriver';

    public static function getNavigationLabel(): string
    {
        return __('client/resources/maintenance_request.navigation_label');
    }

    public static function getPluralModelLabel(): string
    {
        return __('client/resources/maintenance_request.plural_label');
    }

    public static function getModelLabel(): string
    {
        return __('client/resources/maintenance_request.label');
    }

    protected static ?string $slug = 'maintenance-contracts';

    public static function getNavigationGroup(): ?string
    {
        return __('client/navigation.groups.services');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('request_number')
                    ->label(__('filament-resources/maintenance-request.fields.request_number'))
                    ->disabled()
                    ->dehydrated(false),

                Forms\Components\Select::make('contract_type_id')
                    ->label(__('filament-resources/maintenance-request.fields.contract_type_id'))
                    ->options(
                        ContractType::where('is_active', true)
                            ->orderBy('display_order')
                            ->pluck('name', 'id')
                    )
                    ->required(),

                Forms\Components\TextInput::make('visits_included')
                    ->label(__('filament-resources/maintenance-request.fields.visits_included'))
                    ->numeric()
                    ->required(),

                Forms\Components\Select::make('status')
                    ->label(__('filament-resources/maintenance-request.fields.status'))
                    ->options([
                        'new' => __('filament-resources/maintenance-request.status_options.new'),
                        'pending' => __('filament-resources/maintenance-request.status_options.pending'),
                        'approved' => __('filament-resources/maintenance-request.status_options.approved'),
                        'rejected' => __('filament-resources/maintenance-request.status_options.rejected'),
                        'completed' => __('filament-resources/maintenance-request.status_options.completed'),
                    ])
                    ->required(),

                Forms\Components\Textarea::make('notes')
                    ->label(__('filament-resources/maintenance-request.fields.notes'))
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('request_number')
                    ->label(__('client/resources/maintenance_request.columns.request_number'))
                    ->searchable()
                    ->copyable()
                    ->copyMessage(__('client/resources/maintenance_request.messages.request_number_copied'))
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('client.company')
                    ->label(__('client/resources/maintenance_request.columns.client_name'))
                    ->searchable()
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified')),

                Tables\Columns\TextColumn::make('contract_type.name')
                    ->label(__('client/resources/maintenance_request.columns.contract_type'))
                    ->sortable()
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified')),

                Tables\Columns\TextColumn::make('visits_included')
                    ->label(__('client/resources/maintenance_request.columns.visits_count'))
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\BadgeColumn::make('status')
                    ->label(__('client/resources/maintenance_request.columns.status'))
                    ->sortable()
                    ->colors([
                        'warning' => 'new',
                        'info' => 'pending',
                        'primary' => 'assigned',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'gray' => 'canceled',
                    ])
                    ->formatStateUsing(fn (string $state): string => __("client/resources/maintenance_request.status_options.{$state}")),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('client/resources/maintenance_request.columns.created_at'))
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('client/resources/maintenance_request.filters.status'))
                    ->options([
                        'new' => __('client/resources/maintenance_request.status_options.new'),
                        'pending' => __('client/resources/maintenance_request.status_options.pending'),
                        'assigned' => __('client/resources/maintenance_request.status_options.assigned'),
                        'in_progress' => __('client/resources/maintenance_request.status_options.in_progress'),
                        'completed' => __('client/resources/maintenance_request.status_options.completed'),
                        'canceled' => __('client/resources/maintenance_request.status_options.canceled'),
                    ]),

                Tables\Filters\SelectFilter::make('contract_type_id')
                    ->label(__('client/resources/maintenance_request.filters.contract_type'))
                    ->relationship('contract_type', 'name'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label(__('client/resources/maintenance_request.actions.view'))
                    ->icon('heroicon-o-eye'),
                /*Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),*/
            ])
            ->bulkActions([
                /*Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),*/
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaintenanceRequests::route('/'),
            'view' => Pages\ViewMaintenanceRequest::route('/{record}'),
            //'create' => Pages\CreateMaintenanceRequest::route('/create'),
            'edit' => Pages\EditMaintenanceRequest::route('/{record}/edit'),
            // Custom wizard pages
            'wizard' => Pages\MaintenanceWizard::route('/wizard'),
            'wizard.step1' => Pages\MaintenanceWizardSteps\Step1::route('/wizard/step1'),
            'wizard.step2' => Pages\MaintenanceWizardSteps\Step2::route('/wizard/step2'),
            'wizard.step3' => Pages\MaintenanceWizardSteps\Step3::route('/wizard/step3'),
            'wizard.success' => Pages\MaintenanceWizardSteps\Success::route('/wizard/success/{record}'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    // Customize the navigation badge to show pending requests count
    public static function getNavigationBadge(): ?string
    {
        $pendingCount = static::getModel()::where('status', 'pending')->count();
        return $pendingCount > 0 ? (string) $pendingCount : null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    // Add global search
    public static function getGloballySearchableAttributes(): array
    {
        return ['request_number', 'client.name', 'client.company'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            __('client/resources/maintenance_request.search.client') => $record->client?->company ?? $record->client?->name,
            __('client/resources/maintenance_request.search.type') => $record->contract_type?->name,
            __('client/resources/maintenance_request.search.date') => $record->created_at->format('d/m/Y'),
        ];
    }
}
