<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages\MaintenanceWizardSteps;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use App\Filament\Traits\HasMaintenanceRequestHelpers;
use App\Models\MaintenanceRequest;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Session;

class Success extends Page
{
    use HasMaintenanceRequestHelpers;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.success';

    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.wizard.success.title', [], 'تم إرسال الطلب بنجاح');
    }

    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.wizard.success.heading', [], 'تم إرسال طلب الصيانة بنجاح');
    }

    public MaintenanceRequest $record;
    public array $statusConfig = [];
    public string $statusDescription = '';

    public function mount(MaintenanceRequest $record): void
    {
        // Set the record
        $this->record = $record;

        // Get status configuration
        $this->statusConfig = $this->getStatusConfig($record->status) ?? [];

        // Set status description
        $this->statusDescription = $this->getStatusDescription($record->status);

        // Update session data
        $wizardData = [
            'currentStep' => 4,
        ];
        Session::put('maintenance_wizard_data', $wizardData);
    }

    public function startNewRequest(): void
    {
        // Redirect to step 1
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.step1');
    }

    public function viewRequests(): void
    {
        // Redirect to list page
        redirect()->route('filament.client.resources.maintenance-contracts.index');
    }

    /**
     * Get status description for the success page
     */
    protected function getStatusDescription(string $status): string
    {
        return match ($status) {
            'new' => __('filament-resources/maintenance-request.wizard.success.status_descriptions.new', [], 'تم إنشاء طلبك بنجاح وهو الآن في قائمة الانتظار للمراجعة. سيتم التواصل معك قريباً لتأكيد التفاصيل.'),
            'pending' => __('filament-resources/maintenance-request.wizard.success.status_descriptions.pending', [], 'طلبك قيد المراجعة من قبل فريقنا المختص.'),
            'assigned' => __('filament-resources/maintenance-request.wizard.success.status_descriptions.assigned', [], 'تم تعيين فني مختص لطلبك وسيتم التواصل معك لتحديد موعد الزيارة.'),
            'in_progress' => __('filament-resources/maintenance-request.wizard.success.status_descriptions.in_progress', [], 'الفني يعمل حالياً على تنفيذ طلب الصيانة.'),
            'completed' => __('filament-resources/maintenance-request.wizard.success.status_descriptions.completed', [], 'تم إنجاز طلب الصيانة بنجاح.'),
            'canceled' => __('filament-resources/maintenance-request.wizard.success.status_descriptions.canceled', [], 'تم إلغاء طلب الصيانة.'),
            default => __('filament-resources/maintenance-request.wizard.success.status_descriptions.default', [], 'حالة الطلب: :status', ['status' => $status]),
        };
    }

    /**
     * Get the localized status text
     */
    public function getStatusText(): string
    {
        return match ($this->record->status) {
            'new' => __('filament-resources/maintenance-request.status_options.new', [], 'طلب جديد'),
            'pending' => __('filament-resources/maintenance-request.status_options.pending', [], 'قيد المراجعة'),
            'assigned' => __('filament-resources/maintenance-request.status_options.assigned', [], 'تم التعيين'),
            'in_progress' => __('filament-resources/maintenance-request.status_options.in_progress', [], 'قيد التنفيذ'),
            'completed' => __('filament-resources/maintenance-request.status_options.completed', [], 'مكتمل'),
            'canceled' => __('filament-resources/maintenance-request.status_options.canceled', [], 'ملغي'),
            default => $this->record->status,
        };
    }

    /**
     * Get the status color for styling
     */
    public function getStatusColor(): string
    {
        return match ($this->record->status) {
            'new' => 'warning',
            'pending' => 'info',
            'assigned' => 'primary',
            'in_progress' => 'warning',
            'completed' => 'success',
            'canceled' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get next steps information
     */
    public function getNextSteps(): string
    {
        return match ($this->record->status) {
            'new' => __('filament-resources/maintenance-request.wizard.success.next_steps.new', [], 'الخطوات التالية: سيقوم فريقنا بمراجعة طلبك خلال 1-3 أيام عمل وسيتم التواصل معك لتأكيد التفاصيل وتحديد موعد الزيارة.'),
            'pending' => __('filament-resources/maintenance-request.wizard.success.next_steps.pending', [], 'الخطوات التالية: سيتم تعيين فني مختص قريباً.'),
            'assigned' => __('filament-resources/maintenance-request.wizard.success.next_steps.assigned', [], 'الخطوات التالية: سيتواصل معك الفني المعين لتحديد موعد مناسب.'),
            'in_progress' => __('filament-resources/maintenance-request.wizard.success.next_steps.in_progress', [], 'الخطوات التالية: الفني يعمل على إنجاز المهمة.'),
            'completed' => __('filament-resources/maintenance-request.wizard.success.next_steps.completed', [], 'تم إنجاز جميع الأعمال المطلوبة.'),
            default => __('filament-resources/maintenance-request.wizard.success.next_steps.default', [], 'سيتم إعلامك بأي تحديثات على حالة طلبك.'),
        };
    }
}
