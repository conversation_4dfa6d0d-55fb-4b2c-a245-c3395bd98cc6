<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages\MaintenanceWizardSteps;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use App\Filament\Traits\HasMaintenanceRequestHelpers;
use App\Models\MaintenanceRequest;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Session;

class Success extends Page
{
    use HasMaintenanceRequestHelpers;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.success';

    public function getTitle(): string
    {
        return __('client.resources.maintenance_request.wizard.success.title');
    }

    public function getHeading(): string
    {
        return __('client.resources.maintenance_request.wizard.success.heading');
    }

    public MaintenanceRequest $record;
    public array $statusConfig = [];
    public string $statusDescription = '';

    public function mount(MaintenanceRequest $record): void
    {
        // Set the record
        $this->record = $record;

        // Get status configuration
        $this->statusConfig = $this->getStatusConfig($record->status) ?? [];

        // Set status description
        $this->statusDescription = $this->getStatusDescription($record->status);

        // Update session data
        $wizardData = [
            'currentStep' => 4,
        ];
        Session::put('maintenance_wizard_data', $wizardData);
    }

    public function startNewRequest(): void
    {
        // Redirect to step 1
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.step1');
    }

    public function viewRequests(): void
    {
        // Redirect to list page
        redirect()->route('filament.client.resources.maintenance-contracts.index');
    }

    /**
     * Get status description for the success page
     */
    protected function getStatusDescription(string $status): string
    {
        return match ($status) {
            'new' => __('client.resources.maintenance_request.wizard.success.status_descriptions.new'),
            'pending' => __('client.resources.maintenance_request.wizard.success.status_descriptions.pending'),
            'assigned' => __('client.resources.maintenance_request.wizard.success.status_descriptions.assigned'),
            'in_progress' => __('client.resources.maintenance_request.wizard.success.status_descriptions.in_progress'),
            'completed' => __('client.resources.maintenance_request.wizard.success.status_descriptions.completed'),
            'canceled' => __('client.resources.maintenance_request.wizard.success.status_descriptions.canceled'),
            default => __('client.resources.maintenance_request.wizard.success.status_descriptions.default', ['status' => $status]),
        };
    }

    /**
     * Get the localized status text
     */
    public function getStatusText(): string
    {
        return match ($this->record->status) {
            'new' => __('client.resources.maintenance_request.status_options.new'),
            'pending' => __('client.resources.maintenance_request.status_options.pending'),
            'assigned' => __('client.resources.maintenance_request.status_options.assigned'),
            'in_progress' => __('client.resources.maintenance_request.status_options.in_progress'),
            'completed' => __('client.resources.maintenance_request.status_options.completed'),
            'canceled' => __('client.resources.maintenance_request.status_options.canceled'),
            default => $this->record->status,
        };
    }

    /**
     * Get the status color for styling
     */
    public function getStatusColor(): string
    {
        return match ($this->record->status) {
            'new' => 'warning',
            'pending' => 'info',
            'assigned' => 'primary',
            'in_progress' => 'warning',
            'completed' => 'success',
            'canceled' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get next steps information
     */
    public function getNextSteps(): string
    {
        return match ($this->record->status) {
            'new' => __('client.resources.maintenance_request.wizard.success.next_steps.new'),
            'pending' => __('client.resources.maintenance_request.wizard.success.next_steps.pending'),
            'assigned' => __('client.resources.maintenance_request.wizard.success.next_steps.assigned'),
            'in_progress' => __('client.resources.maintenance_request.wizard.success.next_steps.in_progress'),
            'completed' => __('client.resources.maintenance_request.wizard.success.next_steps.completed'),
            default => __('client.resources.maintenance_request.wizard.success.next_steps.default'),
        };
    }
}
