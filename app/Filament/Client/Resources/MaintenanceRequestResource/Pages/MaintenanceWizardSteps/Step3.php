<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages\MaintenanceWizardSteps;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use App\Models\Client;
use App\Models\MaintenanceRequest;
use Carbon\Carbon;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class Step3 extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.step3';

    public function getTitle(): string
    {
        return __('filament-resources/maintenance-request.wizard.step3.title', [], 'مراجعة وتأكيد الطلب');
    }

    public function getHeading(): string
    {
        return __('filament-resources/maintenance-request.wizard.step3.heading', [], 'الخطوة الثالثة: مراجعة وتأكيد الطلب');
    }

    public ?array $data = [];
    public array $contractSummary = [];

    public function mount(): void
    {
        // Get wizard data from session
        $wizardData = Session::get('maintenance_wizard_data', []);

        // Verify that previous steps have been completed
        if (empty($wizardData['contractType']) || empty($wizardData['email'])) {
            redirect()->route('filament.client.resources.maintenance-contracts.wizard.step1');
            return;
        }

        // Update current step
        $wizardData['currentStep'] = 3;
        if (!isset($wizardData['startDate'])) {
            $wizardData['startDate'] = Carbon::now()->addDays(7)->format('Y-m-d');
        }
        Session::put('maintenance_wizard_data', $wizardData);

        // Prepare contract summary
        $this->contractSummary = $this->prepareContractSummary($wizardData);

        // Initialize form data
        $this->data = [
            'termsAgreement' => false,
        ];

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('filament-resources/maintenance-request.wizard.step3.summary_section_title', [], 'ملخص الطلب'))
                    ->schema([
                        ViewField::make('summary')
                            ->view('filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.partials.contract-summary', [
                                'contractSummary' => $this->contractSummary,
                            ]),
                    ]),

                Section::make(__('filament-resources/maintenance-request.wizard.step3.terms_section_title', [], 'الشروط والأحكام'))
                    ->schema([
                        Checkbox::make('termsAgreement')
                            ->label(__('filament-resources/maintenance-request.wizard.step3.fields.terms_agreement', [], 'أوافق على الشروط والأحكام'))
                            ->helperText(__('filament-resources/maintenance-request.wizard.step3.fields.terms_agreement_helper', [], 'اضغط هنا للاطلاع على الشروط والأحكام'))
                            ->required()
                            ->rules(['accepted'])
                            ->validationAttribute(__('filament-resources/maintenance-request.wizard.step3.fields.terms_agreement_validation', [], 'الموافقة على الشروط'))
                            ->columnSpanFull(),
                    ]),
            ])
            ->statePath('data');
    }

    public function submitStep3(): void
    {
        $this->form->validate();

        // Get all the data from the session
        $wizardData = Session::get('maintenance_wizard_data', []);

        $guard = Auth::guard('client');

        // Find or create a client
        $client = $guard->check() ? $guard->user() : Client::firstOrCreate(
            ['phone' => $wizardData['phone']],
            [
                'name' => $wizardData['contactName'],
                'email' => $wizardData['email'],
                'company' => $wizardData['companyName'],
                'address' => $wizardData['address'],
            ]
        );

        $latestRequest = MaintenanceRequest::latest()->first();
        $nextId = $latestRequest ? $latestRequest->id + 1 : 1;
        $requestNumber = 'REQ-' . date('Ym') . '-' . str_pad("$nextId", 4, '0', STR_PAD_LEFT);

        // Create maintenance request
        $maintenanceRequest = MaintenanceRequest::create([
            'request_number' => $requestNumber,
            'client_id' => $client->id,
            'contract_type_id' => $wizardData['contractTypeDetails']['id'],
            'visits_included' => $wizardData['contractTypeDetails']['visit_limit'],
            'status' => 'new',
            'notes' => '',
        ]);

        // Clear wizard session data
        Session::forget('maintenance_wizard_data');

        // Show success notification
        Notification::make()
            ->title(__('filament-resources/maintenance-request.wizard.step3.messages.request_submitted_title', [], 'تم إرسال الطلب بنجاح'))
            ->body(__('filament-resources/maintenance-request.wizard.step3.messages.request_submitted_body', ['number' => $requestNumber], 'رقم الطلب: :number'))
            ->success()
            ->send();

        // Redirect to success page
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.success', [
            'record' => $maintenanceRequest->id,
        ]);
    }

    public function previousStep(): void
    {
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.step2');
    }

    public function showTermsModal(): void
    {
        $this->dispatch('open-modal', id: 'terms-modal');
    }

    /**
     * Helper method to prepare contract summary
     */
    private function prepareContractSummary($data): array
    {
        $startDate = $data['startDate'];
        $endDate = Carbon::parse($startDate)->addMonths($data['contractTypeDetails']['period']);

        return [
            'contractInfo' => [
                'type' => $data['contractTypeDetails']['name'],
                'startDate' => Carbon::parse($startDate)->format('d/m/Y'),
                'endDate' => $endDate->format('d/m/Y'),
                'duration' => $data['contractTypeDetails']['period'] . ' ' . __('filament-resources/maintenance-request.wizard.step3.summary.duration_unit', [], 'شهر'),
                'value' => null,
                'visitLimit' => $data['contractTypeDetails']['visit_limit'],
            ],
            'clientInfo' => [
                'companyName' => $data['companyName'],
                'contactName' => $data['contactName'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'address' => $data['address'],
            ],
        ];
    }
}
