<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages\MaintenanceWizardSteps;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use App\Models\ContractType;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Session;

class Step1 extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.step1';

    public function getTitle(): string
    {
        return __('client.resources.maintenance_request.wizard.step1.title');
    }

    public function getHeading(): string
    {
        return __('client.resources.maintenance_request.wizard.step1.heading');
    }

    public ?array $data = [];
    public $contractTypes = [];

    public function mount(): void
    {
        // Initialize or retrieve wizard data from session
        $wizardData = Session::get('maintenance_wizard_data', [
            'currentStep' => 1,
            'contractType' => null,
            'contractTypeDetails' => null,
        ]);

        $this->data['contractType'] = $wizardData['contractType'] ?? null;

        // Get active contract types
        $this->contractTypes = ContractType::where('is_active', true)
            ->orderBy('display_order')
            ->get()
            ->toArray();

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Hidden::make('contractType'),

                Section::make(__('client.resources.maintenance_request.wizard.step1.section_title'))
                    ->description(__('client.resources.maintenance_request.wizard.step1.section_description'))
                    ->schema([
                        ViewField::make('contract_selection')
                            ->view('filament.client.resources.maintenance-contract-resource.pages.maintenance-wizard-steps.partials.contract-types-buttons', [
                                'contractTypes' => $this->contractTypes,
                            ])
                            ->columnSpanFull(),
                    ]),
            ])
            ->statePath('data');
    }

    public function selectContractType($contractTypeSlug): void
    {
        // Get selected contract type details
        $contractType = ContractType::where('slug', $contractTypeSlug)->first();

        if (!$contractType) {
            Notification::make()
                ->title(__('client.resources.maintenance_request.wizard.step1.messages.error_title'))
                ->body(__('client.resources.maintenance_request.wizard.step1.messages.invalid_contract_type'))
                ->danger()
                ->send();

            return;
        }

        // Store data in session
        $wizardData = Session::get('maintenance_wizard_data', []);
        $wizardData['currentStep'] = 1;
        $wizardData['contractType'] = $contractTypeSlug;
        $wizardData['contractTypeDetails'] = [
            'id' => $contractType->id,
            'name' => $contractType->name,
            'period' => $contractType->period,
            'visit_limit' => $contractType->visit_limit,
        ];
        $wizardData['contractDuration'] = $contractType->period;

        Session::put('maintenance_wizard_data', $wizardData);

        // Show success notification
        Notification::make()
            ->title(__('client.resources.maintenance_request.wizard.step1.messages.contract_selected_title'))
            ->body(__('client.resources.maintenance_request.wizard.step1.messages.contract_selected_body', ['name' => $contractType->name]))
            ->success()
            ->duration(1500)
            ->send();

        // Auto-redirect to next step after a short delay
        $this->redirect(route('filament.client.resources.maintenance-contracts.wizard.step2'), navigate: true);
    }

    public function submitStep1(): void
    {
        $this->form->validate();

        if (empty($this->data['contractType'])) {
            Notification::make()
                ->title(__('client.resources.maintenance_request.wizard.step1.messages.error_title'))
                ->body(__('client.resources.maintenance_request.wizard.step1.messages.please_select_contract_type'))
                ->danger()
                ->send();

            return;
        }

        // Get selected contract type details
        $selectedContractType = $this->data['contractType'];
        $contractType = ContractType::where('slug', $selectedContractType)->first();

        if (!$contractType) {
            Notification::make()
                ->title(__('client.resources.maintenance_request.wizard.step1.messages.error_title'))
                ->body(__('client.resources.maintenance_request.wizard.step1.messages.invalid_contract_type'))
                ->danger()
                ->send();

            return;
        }

        // Store data in session
        $wizardData = Session::get('maintenance_wizard_data', []);
        $wizardData['currentStep'] = 1;
        $wizardData['contractType'] = $selectedContractType;
        $wizardData['contractTypeDetails'] = [
            'id' => $contractType->id,
            'name' => $contractType->name,
            'period' => $contractType->period,
            'visit_limit' => $contractType->visit_limit,
        ];
        $wizardData['contractDuration'] = $contractType->period;

        Session::put('maintenance_wizard_data', $wizardData);

        // Redirect to next step
        redirect()->route('filament.client.resources.maintenance-contracts.wizard.step2');
    }
}
