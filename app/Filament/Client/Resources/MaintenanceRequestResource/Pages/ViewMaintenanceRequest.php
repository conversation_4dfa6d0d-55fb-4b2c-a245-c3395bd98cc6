<?php

declare(strict_types=1);

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use App\Filament\Traits\HasMaintenanceRequestHelpers;
use App\Models\MaintenanceRequest;
use App\Services\DocumentTemplateService;
use App\Services\PdfStorageService;
use Exception;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;

/**
 * Enhanced ViewMaintenanceRequest Page
 *
 * Provides a comprehensive view of maintenance requests with optimized performance,
 * improved user experience, and robust error handling. Features include:
 * - Cached data for improved performance
 * - Responsive design with accessibility support
 * - Enhanced PDF generation with loading states
 * - Comprehensive error handling and user feedback
 * - Modular, reusable component architecture
 *
 * @package App\Filament\Client\Resources\MaintenanceRequestResource\Pages
 * <AUTHOR> Development Team
 * @version 2.0.0
 */
class ViewMaintenanceRequest extends ViewRecord
{
    use HasMaintenanceRequestHelpers;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.view-maintenance-request';

    /**
     * PDF URL for generated documents
     */
    public ?string $pdfUrl = null;

    /**
     * Loading state for PDF generation
     */
    public bool $isGeneratingPdf = false;

    /**
     * Cached status configuration
     */
    public array $statusConfig = [];

    /**
     * Cached payment summary
     */
    public array $paymentSummary = [];

    /**
     * Cache TTL in minutes
     */
    protected int $cacheMinutes = 5;


    /**
     * Get the page title compatible with Admin version
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('client.resources.maintenance_request.pages.view.title', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Get the page heading compatible with Admin version
     *
     * @return string
     */
    public function getHeading(): string
    {
        return __('client.resources.maintenance_request.pages.view.heading', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Mount the page with optimized data loading and caching
     *
     * @param int|string $record The record identifier
     * @return void
     */
    public function mount(int|string $record): void
    {
        parent::mount($record);

        $this->optimizeRecordLoading();
        $this->initializeCachedData();
    }

    /**
     * Optimize record loading with eager loading to prevent N+1 queries
     *
     * @return void
     */
    protected function optimizeRecordLoading(): void
    {
        // Eager load all necessary relationships to prevent N+1 queries
        $this->record->load([
            'client:id,name,phone,email,address',
            'contract:id,maintenance_request_id,contract_number,start_date,end_date,status,terms',
            'contractType:id,name,description,period,benefits',
            'assignedTechnician:id,name,phone,email',
            'payments:id,maintenance_request_id,amount,status,created_at'
        ]);
    }

    /**
     * Initialize cached data for improved performance
     *
     * @return void
     */
    protected function initializeCachedData(): void
    {
        // Cache status configuration
        $this->statusConfig = $this->getStatusConfig($this->record->status);

        // Cache payment summary
        $this->paymentSummary = $this->getPaymentSummary($this->record);
    }

    /**
     * Configure the enhanced infolist with optimized performance
     * Compatible with Admin version structure while maintaining client enhancements
     *
     * @param Infolist $infolist
     * @return Infolist
     */
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Header Summary Cards - Client-specific enhancement
                $this->buildHeaderSummaryCards(),

                // Main Content Grid - Compatible with Admin 3-column structure
                $this->buildCompatibleMainContentGrid(),

                // Progress Timeline - Interactive and informative
                $this->buildProgressTimeline(),

                // Additional Information Tabs - Lazy loaded for performance
                $this->buildAdditionalInfoTabs(),
            ]);
    }

    /**
     * Build header summary cards with cached status configuration
     *
     * @return Grid
     */
    protected function buildHeaderSummaryCards(): Grid
    {
        if (empty($this->statusConfig)) {
            $this->initializeCachedData();
        }
        return Grid::make(4)
            ->schema([
                // Request Status Card
                Section::make(__('client.resources.maintenance_request.cards.status'))
                    ->schema([
                        TextEntry::make('status')
                            ->label('')
                            ->badge()
                            ->size(TextEntry\TextEntrySize::Large)
                            ->color($this->statusConfig['color'])
                            ->formatStateUsing(fn(): string => $this->statusConfig['text'])
                            ->extraAttributes([
                                'class' => 'status-badge-enhanced',
                                'aria-label' => __('client.resources.maintenance_request.aria.status_label') . ': ' . $this->statusConfig['text']
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),

                // Request Number Card
                Section::make(__('client.resources.maintenance_request.cards.request_number'))
                    ->schema([
                        TextEntry::make('request_number')
                            ->label('')
                            ->weight(FontWeight::Bold)
                            ->size(TextEntry\TextEntrySize::Large)
                            ->color('primary')
                            ->copyable()
                            ->copyMessage(__('client.resources.maintenance_request.messages.request_number_copied'))
                            ->copyMessageDuration(2000)
                            ->extraAttributes([
                                'aria-label' => __('client.resources.maintenance_request.aria.request_number_label') . ': ' . $this->record->request_number
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),

                // Contract Type Card
                Section::make(__('filament-resources/maintenance-request.cards.contract_type', [], 'نوع العقد'))
                    ->schema([
                        TextEntry::make('contractType.name')
                            ->label('')
                            ->weight(FontWeight::Bold)
                            ->placeholder('غير محدد')
                            ->extraAttributes([
                                'aria-label' => 'نوع العقد: ' . ($this->record->contractType?->name ?? 'غير محدد')
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),

                // Creation Date Card
                Section::make(__('filament-resources/maintenance-request.cards.creation_date', [], 'تاريخ الطلب'))
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('')
                            ->dateTime('d/m/Y H:i')
                            ->weight(FontWeight::Medium)
                            ->extraAttributes([
                                'aria-label' => 'تاريخ إنشاء الطلب: ' . $this->record->created_at->format('d/m/Y H:i')
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),
            ])
            ->extraAttributes(['class' => 'header-summary-grid']);
    }

    /**
     * Build main content grid compatible with Admin version structure
     * Maintains 3-column layout while preserving client enhancements
     *
     * @return Grid
     */
    protected function buildCompatibleMainContentGrid(): Grid
    {
        return Grid::make(3)
            ->schema([
                // Request Details Column - Compatible with Admin structure
                $this->buildCompatibleRequestDetailsSection(),

                // Client & Contract Information Column - Merged for compatibility
                $this->buildCompatibleClientContractSection(),

                // Assignment & Financial Information Column - Admin-compatible
                $this->buildCompatibleAssignmentFinancialSection(),
            ])
            ->extraAttributes(['class' => 'main-content-grid']);
    }

    /**
     * Build main content grid with optimized data display (Legacy method)
     * Kept for backward compatibility
     *
     * @return Grid
     */
    protected function buildMainContentGrid(): Grid
    {
        return $this->buildCompatibleMainContentGrid();
    }

    /**
     * Build request details section compatible with Admin version
     * Includes title field and uses translation keys
     *
     * @return Section
     */
    protected function buildCompatibleRequestDetailsSection(): Section
    {
        return Section::make(__('filament-resources/maintenance-request.sections.request_details', [], 'تفاصيل الطلب'))
            ->schema([
                TextEntry::make('request_number')
                    ->label(__('filament-resources/maintenance-request.fields.request_number', [], 'رقم الطلب'))
                    ->weight(FontWeight::Bold)
                    ->copyable()
                    ->copyMessage(__('filament-resources/maintenance-request.messages.request_number_copied', [], 'تم نسخ رقم الطلب'))
                    ->copyMessageDuration(1500)
                    ->extraAttributes(['aria-label' => 'رقم الطلب']),

                TextEntry::make('title')
                    ->label(__('filament-resources/maintenance-request.fields.title', [], 'عنوان الطلب'))
                    ->weight(FontWeight::Medium)
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_title', [], 'لا يوجد عنوان'))
                    ->extraAttributes(['aria-label' => 'عنوان الطلب']),

                TextEntry::make('created_at')
                    ->label(__('filament-resources/maintenance-request.fields.created_at', [], 'تاريخ الإنشاء'))
                    ->dateTime('d/m/Y H:i')
                    ->extraAttributes(['aria-label' => 'تاريخ إنشاء الطلب']),

                TextEntry::make('status')
                    ->label(__('filament-resources/maintenance-request.fields.status', [], 'الحالة'))
                    ->badge()
                    ->color($this->statusConfig['color'])
                    ->formatStateUsing(fn(): string => $this->statusConfig['text'])
                    ->extraAttributes(['aria-label' => 'حالة الطلب']),

                TextEntry::make('visits_included')
                    ->label(__('filament-resources/maintenance-request.fields.visits_included', [], 'عدد الزيارات المشمولة'))
                    ->badge()
                    ->color('info')
                    ->extraAttributes(['aria-label' => 'عدد الزيارات المشمولة']),

                TextEntry::make('notes')
                    ->label(__('filament-resources/maintenance-request.fields.notes', [], 'ملاحظات'))
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_notes', [], 'لا توجد ملاحظات'))
                    ->markdown()
                    ->limit(150)
                    ->tooltip(fn(?string $state): ?string => $state && strlen($state) > 150 ? $state : null)
                    ->extraAttributes(['aria-label' => 'ملاحظات الطلب']),
            ])
            ->columnSpan(1)
            ->extraAttributes(['class' => 'enhanced-card request-details']);
    }

    /**
     * Build request details section (Legacy method)
     * Kept for backward compatibility
     *
     * @return Section
     */
    protected function buildRequestDetailsSection(): Section
    {
        return $this->buildCompatibleRequestDetailsSection();
    }

    /**
     * Build client & contract information section compatible with Admin version
     * Combines client and contract info to match Admin 3-column layout
     *
     * @return Section
     */
    protected function buildCompatibleClientContractSection(): Section
    {
        return Section::make(__('filament-resources/maintenance-request.sections.client_contract', [], 'معلومات العميل والعقد'))
            ->schema([
                TextEntry::make('client.name')
                    ->label(__('filament-resources/maintenance-request.fields.client_id', [], 'العميل'))
                    ->weight(FontWeight::Bold)
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_specified', [], 'غير محدد'))
                    ->url(fn($record) => $record->client_id
                        ? MaintenanceRequestResource::getUrl('index', ['tableFilters' => ['client_id' => ['value' => $record->client_id]]])
                        : null)
                    ->extraAttributes(['aria-label' => 'اسم العميل']),

                TextEntry::make('client.phone')
                    ->label(__('filament-resources/maintenance-request.fields.client_phone', [], 'رقم الهاتف'))
                    ->icon('heroicon-o-phone')
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_specified', [], 'غير محدد'))
                    ->copyable()
                    ->copyMessage(__('filament-resources/maintenance-request.messages.phone_copied', [], 'تم نسخ رقم الهاتف'))
                    ->url(fn(?string $state): ?string => $state ? "tel:{$state}" : null)
                    ->extraAttributes(['aria-label' => 'رقم الهاتف']),

                TextEntry::make('client.email')
                    ->label(__('filament-resources/maintenance-request.fields.client_email', [], 'البريد الإلكتروني'))
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_specified', [], 'غير محدد'))
                    ->copyable()
                    ->copyMessage(__('filament-resources/maintenance-request.messages.email_copied', [], 'تم نسخ البريد الإلكتروني'))
                    ->url(fn(?string $state): ?string => $state ? "mailto:{$state}" : null)
                    ->extraAttributes(['aria-label' => 'البريد الإلكتروني']),

                TextEntry::make('contract.contract_number')
                    ->label(__('filament-resources/maintenance-request.fields.contract_number', [], 'رقم العقد'))
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract', [], 'لم يتم إنشاء العقد بعد'))
                    ->copyable()
                    ->copyMessage(__('filament-resources/maintenance-request.messages.contract_number_copied', [], 'تم نسخ رقم العقد'))
                    ->extraAttributes(['aria-label' => 'رقم العقد']),

                TextEntry::make('contractType.name')
                    ->label(__('filament-resources/maintenance-request.fields.contract_type', [], 'نوع العقد'))
                    ->weight(FontWeight::Bold)
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_specified', [], 'غير محدد'))
                    ->extraAttributes(['aria-label' => 'نوع العقد']),

                TextEntry::make('contract.status')
                    ->label(__('filament-resources/maintenance-request.fields.contract_status', [], 'حالة العقد'))
                    ->badge()
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract', [], 'لا يوجد عقد'))
                    ->color(fn(?string $state): string => match ($state) {
                        'pending' => 'warning',
                        'active' => 'success',
                        'expired' => 'danger',
                        'terminated' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(?string $state): string => $this->getContractStatusText($state))
                    ->extraAttributes(['aria-label' => 'حالة العقد']),
            ])
            ->columnSpan(1)
            ->extraAttributes(['class' => 'enhanced-card client-contract-info']);
    }

    /**
     * Build client information section (Legacy method)
     * Kept for backward compatibility
     *
     * @return Section
     */
    protected function buildClientInformationSection(): Section
    {
        return $this->buildCompatibleClientContractSection();
    }

    /**
     * Build assignment & financial information section compatible with Admin version
     * Includes assigned technician and price fields like Admin version
     *
     * @return Section
     */
    protected function buildCompatibleAssignmentFinancialSection(): Section
    {
        return Section::make(__('filament-resources/maintenance-request.sections.assignment_financial', [], 'التكليف والمالية'))
            ->schema([
                TextEntry::make('assignedTechnician.name')
                    ->label(__('filament-resources/maintenance-request.fields.assigned_technician', [], 'الفني المكلف'))
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_assigned', [], 'لم يتم التكليف بعد'))
                    ->weight(FontWeight::Medium)
                    ->extraAttributes(['aria-label' => 'الفني المكلف']),

                TextEntry::make('price')
                    ->label(__('filament-resources/maintenance-request.fields.price', [], 'السعر'))
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_priced', [], 'لم يتم تحديد السعر بعد'))
                    ->formatStateUsing(fn(?float $state): string => $state ? number_format($state, 2) . ' ريال' : 'لم يتم تحديد السعر بعد')
                    ->color('success')
                    ->weight(FontWeight::Bold)
                    ->extraAttributes(['aria-label' => 'السعر']),

                TextEntry::make('payment_status')
                    ->label(__('filament-resources/maintenance-request.fields.payment_status', [], 'حالة الدفع'))
                    ->state(fn(): string => $this->paymentSummary['status'] ?? __('filament-resources/maintenance-request.placeholders.no_payments', [], 'لا توجد مدفوعات'))
                    ->badge()
                    ->color(fn(): string => $this->paymentSummary['status_color'] ?? 'gray')
                    ->extraAttributes(['aria-label' => 'حالة الدفع']),

                TextEntry::make('contractType.period')
                    ->label(__('filament-resources/maintenance-request.fields.contract_period', [], 'مدة العقد'))
                    ->formatStateUsing(fn(?int $state): string => $state ? $state . ' ' . __('filament-resources/maintenance-request.units.months', [], 'شهر') : __('filament-resources/maintenance-request.placeholders.not_specified', [], 'غير محدد'))
                    ->extraAttributes(['aria-label' => 'مدة العقد']),

                TextEntry::make('contract.start_date')
                    ->label(__('filament-resources/maintenance-request.fields.contract_start_date', [], 'تاريخ بداية العقد'))
                    ->date('d/m/Y')
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_set', [], 'لم يتم تحديده بعد'))
                    ->extraAttributes(['aria-label' => 'تاريخ بداية العقد']),

                TextEntry::make('contract.end_date')
                    ->label(__('filament-resources/maintenance-request.fields.contract_end_date', [], 'تاريخ انتهاء العقد'))
                    ->date('d/m/Y')
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.not_set', [], 'لم يتم تحديده بعد'))
                    ->extraAttributes(['aria-label' => 'تاريخ انتهاء العقد']),
            ])
            ->columnSpan(1)
            ->extraAttributes(['class' => 'enhanced-card assignment-financial']);
    }

    /**
     * Build contract and financial information section (Legacy method)
     * Kept for backward compatibility
     *
     * @return Section
     */
    protected function buildContractFinancialSection(): Section
    {
        return $this->buildCompatibleAssignmentFinancialSection();
    }

    /**
     * Build enhanced progress timeline
     *
     * @return Section
     */
    protected function buildProgressTimeline(): Section
    {
        return Section::make(__('filament-resources/maintenance-request.sections.progress_timeline', [], 'مراحل معالجة الطلب'))
            ->schema([
                TextEntry::make('progress_timeline')
                    ->label('')
                    ->state(function (): string {
                        return $this->generateTimelineContent();
                    })
                    ->markdown()
                    ->columnSpanFull()
                    ->extraAttributes([
                        'class' => 'timeline-container',
                        'aria-label' => __('filament-resources/maintenance-request.aria.progress_timeline', [], 'مراحل معالجة الطلب')
                    ]),
            ])
            ->collapsible()
            ->collapsed(false)
            ->extraAttributes(['class' => 'enhanced-card timeline-section']);
    }

    /**
     * Generate timeline content based on request status
     *
     * @return string
     */
    protected function generateTimelineContent(): string
    {
        $timeline = [];
        $status = $this->record->status;

        // Step 1: Request Created
        $timeline[] = '✅ **' . __('filament-resources/maintenance-request.timeline.request_created', [], 'تم إنشاء الطلب') . '** - ' . $this->record->created_at->format('d/m/Y H:i');

        // Step 2: Under Review
        if (in_array($status, ['assigned', 'in_progress', 'completed'])) {
            $timeline[] = '✅ **' . __('filament-resources/maintenance-request.timeline.under_review', [], 'قيد المراجعة') . '** - ' . __('filament-resources/maintenance-request.timeline.review_completed', [], 'تمت مراجعة الطلب');
        } elseif ($status === 'pending') {
            $timeline[] = '⏳ **' . __('filament-resources/maintenance-request.timeline.under_review', [], 'قيد المراجعة') . '** - ' . __('filament-resources/maintenance-request.timeline.awaiting_review', [], 'في انتظار المراجعة');
        } else {
            $timeline[] = '⏸️ **' . __('filament-resources/maintenance-request.timeline.under_review', [], 'قيد المراجعة') . '** - ' . __('filament-resources/maintenance-request.timeline.awaiting_review', [], 'في انتظار المراجعة');
        }

        // Step 3: Technician Assignment
        if (in_array($status, ['assigned', 'in_progress', 'completed'])) {
            $technicianName = $this->record->assignedTechnician?->name ?? __('filament-resources/maintenance-request.timeline.technician_assigned', [], 'تم تعيين فني');
            $timeline[] = '✅ **' . __('filament-resources/maintenance-request.timeline.technician_assigned', [], 'تم تعيين الفني') . '** - ' . $technicianName;
        } elseif ($status === 'pending') {
            $timeline[] = '⏳ **' . __('filament-resources/maintenance-request.timeline.awaiting_assignment', [], 'في انتظار تعيين الفني') . '**';
        } else {
            $timeline[] = '⏸️ **' . __('filament-resources/maintenance-request.timeline.awaiting_assignment', [], 'في انتظار تعيين الفني') . '**';
        }

        // Step 4: Work in Progress
        if (in_array($status, ['in_progress', 'completed'])) {
            $timeline[] = '✅ **' . __('filament-resources/maintenance-request.timeline.work_in_progress', [], 'العمل قيد التنفيذ') . '**';
        } elseif ($status === 'assigned') {
            $timeline[] = '⏳ **' . __('filament-resources/maintenance-request.timeline.awaiting_work_start', [], 'في انتظار بدء العمل') . '**';
        } else {
            $timeline[] = '⏸️ **' . __('filament-resources/maintenance-request.timeline.awaiting_work_start', [], 'في انتظار بدء العمل') . '**';
        }

        // Step 5: Contract Creation (Optional)
        if ($this->record->contract) {
            $contractNumber = $this->record->contract->contract_number;
            $timeline[] = "✅ **" . __('filament-resources/maintenance-request.timeline.contract_created', [], 'تم إنشاء العقد') . "** - " . __('filament-resources/maintenance-request.timeline.contract_number', [], 'رقم العقد') . ": {$contractNumber}";
        } elseif (in_array($status, ['assigned', 'in_progress'])) {
            $timeline[] = '⏳ **' . __('filament-resources/maintenance-request.timeline.awaiting_contract_creation', [], 'في انتظار إنشاء العقد') . '**';
        } else {
            $timeline[] = '⏸️ **' . __('filament-resources/maintenance-request.timeline.contract_creation', [], 'إنشاء العقد') . '** - ' . __('filament-resources/maintenance-request.timeline.awaiting_assignment', [], 'في انتظار تعيين الفني');
        }

        // Step 6: Completion
        if ($status === 'completed') {
            $timeline[] = '✅ **' . __('filament-resources/maintenance-request.timeline.processing_completed', [], 'تم اكتمال الصيانة') . '**';
        } else {
            $timeline[] = '⏳ **' . __('filament-resources/maintenance-request.timeline.processing_completion', [], 'اكتمال الصيانة') . '** - ' . __('filament-resources/maintenance-request.timeline.awaiting_final_procedures', [], 'في انتظار إنهاء أعمال الصيانة');
        }

        return implode("\n\n", $timeline);
    }

    /**
     * Build additional information tabs with lazy loading
     *
     * @return Tabs
     */
    protected function buildAdditionalInfoTabs(): Tabs
    {
        return Tabs::make(__('filament-resources/maintenance-request.sections.additional_info', [], 'معلومات إضافية'))
            ->tabs([
                $this->buildNotesDetailsTab(),
                $this->buildPaymentsTab(),
                $this->buildContractTab(),
            ])
            ->columnSpanFull()
            ->persistTabInQueryString()
            ->extraAttributes(['class' => 'enhanced-tabs']);
    }

    /**
     * Build notes and details tab
     *
     * @return Tabs\Tab
     */
    protected function buildNotesDetailsTab(): Tabs\Tab
    {
        return Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.notes_details', [], 'الملاحظات والتفاصيل'))
            ->schema([
                TextEntry::make('notes')
                    ->label(__('filament-resources/maintenance-request.fields.notes', [], 'الملاحظات'))
                    ->markdown()
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_notes_available', [], 'لا توجد ملاحظات متاحة'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.notes', [], 'ملاحظات الطلب')]),

                TextEntry::make('contractType.description')
                    ->label(__('filament-resources/maintenance-request.fields.contract_type_description', [], 'وصف نوع العقد'))
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_description_available', [], 'لا يوجد وصف متاح'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_type_description', [], 'وصف نوع العقد')]),

                TextEntry::make('contractType.benefits')
                    ->label(__('filament-resources/maintenance-request.fields.contract_benefits', [], 'مزايا العقد'))
                    ->state(function (): string {
                        $benefits = $this->record->contractType?->benefits ?? [];
                        return is_array($benefits) && !empty($benefits)
                            ? implode("\n• ", [''] + $benefits)
                            : __('filament-resources/maintenance-request.placeholders.no_benefits_specified', [], 'لا توجد مزايا محددة');
                    })
                    ->markdown()
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_benefits', [], 'مزايا العقد')]),
            ])
            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.notes_details_tab', [], 'تبويب الملاحظات والتفاصيل')]);
    }

    /**
     * Build payments tab with cached data
     *
     * @return Tabs\Tab
     */
    protected function buildPaymentsTab(): Tabs\Tab
    {
        return Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.payments', [], 'المدفوعات'))
            ->schema([
                Grid::make(3)
                    ->schema([
                        TextEntry::make('payments_total')
                            ->label(__('filament-resources/maintenance-request.fields.total_amount_required', [], 'إجمالي المبلغ المطلوب'))
                            ->state(fn(): string => $this->paymentSummary['total_amount'] > 0
                                ? number_format($this->paymentSummary['total_amount'], 2) . ' ' . __('filament-resources/maintenance-request.units.riyal', [], 'ريال')
                                : __('filament-resources/maintenance-request.placeholders.not_set_yet', [], 'لم يتم تحديده بعد')
                            )
                            ->color('primary')
                            ->weight(FontWeight::Bold)
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.total_amount_required', [], 'إجمالي المبلغ المطلوب')]),

                        TextEntry::make('payments_paid')
                            ->label(__('filament-resources/maintenance-request.fields.amount_paid', [], 'المبلغ المدفوع'))
                            ->state(fn(): string => number_format($this->paymentSummary['paid_amount'] ?? 0, 2) . ' ' . __('filament-resources/maintenance-request.units.riyal', [], 'ريال')
                            )
                            ->color('success')
                            ->weight(FontWeight::Bold)
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.amount_paid', [], 'المبلغ المدفوع')]),

                        TextEntry::make('payments_pending')
                            ->label(__('filament-resources/maintenance-request.fields.amount_remaining', [], 'المبلغ المتبقي'))
                            ->state(fn(): string => number_format($this->paymentSummary['pending_amount'] ?? 0, 2) . ' ' . __('filament-resources/maintenance-request.units.riyal', [], 'ريال')
                            )
                            ->color('warning')
                            ->weight(FontWeight::Bold)
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.amount_remaining', [], 'المبلغ المتبقي')]),
                    ]),

                TextEntry::make('payment_instructions')
                    ->label(__('filament-resources/maintenance-request.fields.payment_instructions', [], 'تعليمات الدفع'))
                    ->state(__('filament-resources/maintenance-request.messages.payment_instructions', [], 'سيتم إرسال تفاصيل الدفع عبر البريد الإلكتروني أو الهاتف بعد اعتماد الطلب.'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.payment_instructions', [], 'تعليمات الدفع')]),
            ])
            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.payments_tab', [], 'تبويب المدفوعات')]);
    }

    /**
     * Build contract tab
     *
     * @return Tabs\Tab
     */
    protected function buildContractTab(): Tabs\Tab
    {
        return Tabs\Tab::make(__('filament-resources/maintenance-request.tabs.linked_contract', [], 'العقد المرتبط'))
            ->schema([
                Section::make(__('filament-resources/maintenance-request.sections.contract_details', [], 'تفاصيل العقد'))
                    ->schema([
                        TextEntry::make('contract.contract_number')
                            ->label(__('filament-resources/maintenance-request.fields.contract_number', [], 'رقم العقد'))
                            ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract_created', [], 'لم يتم إنشاء العقد بعد'))
                            ->copyable()
                            ->copyMessage(__('filament-resources/maintenance-request.messages.contract_number_copied', [], 'تم نسخ رقم العقد'))
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_number', [], 'رقم العقد')]),

                        TextEntry::make('contract.start_date')
                            ->label(__('filament-resources/maintenance-request.fields.start_date', [], 'تاريخ البداية'))
                            ->date('d/m/Y')
                            ->placeholder(__('filament-resources/maintenance-request.placeholders.not_set', [], 'لم يتم تحديده'))
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_start_date', [], 'تاريخ بداية العقد')]),

                        TextEntry::make('contract.end_date')
                            ->label(__('filament-resources/maintenance-request.fields.end_date', [], 'تاريخ الانتهاء'))
                            ->date('d/m/Y')
                            ->placeholder(__('filament-resources/maintenance-request.placeholders.not_set', [], 'لم يتم تحديده'))
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_end_date', [], 'تاريخ انتهاء العقد')]),

                        TextEntry::make('contract.status')
                            ->label(__('filament-resources/maintenance-request.fields.contract_status', [], 'حالة العقد'))
                            ->badge()
                            ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract', [], 'لا يوجد عقد'))
                            ->color(fn(?string $state): string => match ($state) {
                                'pending' => 'warning',
                                'active' => 'success',
                                'expired' => 'danger',
                                'terminated' => 'danger',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn(?string $state): string => $this->getContractStatusText($state))
                            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_status', [], 'حالة العقد')]),
                    ])
                    ->columns(4),

                TextEntry::make('contract.terms')
                    ->label(__('filament-resources/maintenance-request.fields.contract_terms', [], 'شروط العقد'))
                    ->markdown()
                    ->placeholder(__('filament-resources/maintenance-request.placeholders.no_terms_set', [], 'لم يتم تحديد الشروط بعد'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contract_terms', [], 'شروط العقد')]),
            ])
            ->visible(fn(): bool => $this->record->contract_id !== null)
            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.linked_contract_tab', [], 'تبويب العقد المرتبط')]);
    }

    /**
     * Public method to generate PDF for the current maintenance request
     *
     * @return void
     */
    public function generatePdf(): void
    {
        // Use the trait method for PDF generation
        $this->generateMaintenanceRequestPdf($this->record);
    }

    /**
     * Generate PDF specifically for modal display with persistent storage
     *
     * @return void
     */
    public function generatePdfForModal(): void
    {
        try {
            // Check if a valid PDF already exists and doesn't need regeneration
            if (!$this->record->needsPdfRegeneration()) {
                $existingUrl = PdfStorageService::getPdfUrl($this->record);
                if ($existingUrl) {
                    $this->pdfUrl = $existingUrl;

                    $this->sendPdfLoadedNotification();

                    Log::info('Using existing PDF for modal display', [
                        'maintenance_request_id' => $this->record->id,
                        'pdf_url' => $existingUrl,
                        'pdf_generated_at' => $this->record->pdf_generated_at
                    ]);

                    return;
                }
            }

            // Use the trait method for PDF generation with storage
            $this->generateMaintenanceRequestPdf($this->record);

            // Send appropriate notification based on the result
            if (!empty($this->pdfUrl)) {
                // Check if PDF was stored persistently or is temporary
                if ($this->record->hasPdf()) {
                    $this->sendPdfGeneratedNotification();
                } else {
                    $this->sendPdfTemporaryNotification();
                }
            }

        } catch (Exception $e) {
            Log::error('PDF generation failed in modal', [
                'error' => $e->getMessage(),
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendPdfErrorNotification();
        }
    }



    /**
     * Send notification for loaded existing PDF
     *
     * @return void
     */
    protected function sendPdfLoadedNotification(): void
    {
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.pdf_loaded', [], 'تم تحميل ملف PDF المحفوظ'))
            ->body(__('filament-resources/maintenance-request.notifications.pdf_loaded_body', [], 'تم استخدام النسخة المحفوظة من ملف PDF'))
            ->success()
            ->duration(2000)
            ->send();
    }

    /**
     * Send notification for successfully generated and stored PDF
     *
     * @return void
     */
    protected function sendPdfGeneratedNotification(): void
    {
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.pdf_generated', [], 'تم إنشاء ملف PDF بنجاح'))
            ->body(__('filament-resources/maintenance-request.notifications.pdf_generated_body', [], 'يمكنك الآن معاينة الملف أو تحميله'))
            ->success()
            ->duration(3000)
            ->send();
    }

    /**
     * Send notification for temporary PDF (storage failed)
     *
     * @return void
     */
    protected function sendPdfTemporaryNotification(): void
    {
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.pdf_generated_temp', [], 'تم إنشاء ملف PDF مؤقت'))
            ->body(__('filament-resources/maintenance-request.notifications.pdf_storage_failed', [], 'تم إنشاء الملف ولكن فشل حفظه. يمكنك معاينته وتحميله الآن.'))
            ->warning()
            ->duration(4000)
            ->send();
    }

    /**
     * Send notification for PDF generation error
     *
     * @return void
     */
    protected function sendPdfErrorNotification(): void
    {
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.pdf_generation_failed', [], 'فشل إنشاء ملف PDF'))
            ->body(__('filament-resources/maintenance-request.notifications.pdf_generation_failed_body', [], 'حدث خطأ أثناء إنشاء ملف PDF. يرجى المحاولة مرة أخرى.'))
            ->danger()
            ->duration(5000)
            ->send();
    }

    /**
     * Build PDF modal content with viewer
     *
     * @return View
     */
    protected function buildPdfModalContent(): View
    {
        $this->generatePdfForModal();
        return view('filament.components.pdf-modal-content', [
            'pdfUrl' => $this->pdfUrl,
            'isGenerating' => $this->isGeneratingPdf,
            'record' => $this->record,
            'isPersistent' => $this->record->hasPdf(),
            'fileSize' => $this->record->getPdfFileSizeFormatted(),
            'generatedAt' => $this->record->pdf_generated_at,
        ]);
    }

    /**
     * Get enhanced header actions with improved UX and accessibility
     *
     * @return array<Action>
     */
    protected function getHeaderActions(): array
    {
        return [
            $this->buildPrintPdfAction(),
            $this->buildDownloadContractAction(),
            $this->buildContactSupportAction(),
            $this->buildCancelRequestAction(),
        ];
    }

    /**
     * Build generate PDF action with modal viewer
     *
     * @return Action
     */
    protected function buildPrintPdfAction(): Action
    {
        return Action::make('print_pdf')
            ->label(__('filament-resources/maintenance-request.actions.print_request', [], 'طباعة الطلب'))
            ->color('warning')
            ->icon('heroicon-o-document-arrow-down')
            ->tooltip(__('filament-resources/maintenance-request.tooltips.print_request', [], 'طباعة الطلب'))
            ->disabled(fn(): bool => $this->isGeneratingPdf)
            ->modal()
            ->modalHeading(__('filament-resources/maintenance-request.actions.print_request', [], 'طباعة الطلب'))
            ->modalDescription(__('filament-resources/maintenance-request.tooltips.print_request', [], 'معاينة وطباعة طلب الصيانة'))
            ->modalWidth('7xl')
            ->modalContent(function (): View {
                return $this->buildPdfModalContent();
            })
            ->slideOver()
            ->modalActions([
                Action::make('download')
                    ->label(__('filament-resources/maintenance-request.actions.download_pdf', [], 'تحميل PDF'))
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(fn(): ?string => $this->pdfUrl)
                    ->openUrlInNewTab()
                    ->visible(fn(): bool => !empty($this->pdfUrl)),
                Action::make('close')
                    ->label(__('filament-resources/maintenance-request.actions.close', [], 'إغلاق'))
                    ->color('gray')
                    ->modalCancelAction(),
            ])

            ->visible(fn(): bool => $this->canPerformAction($this->record, 'print_pdf'))
            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.generate_pdf', [], 'إنشاء ملف PDF')]);
    }

    /**
     * Build download contract action
     *
     * @return Action
     */
    protected function buildDownloadContractAction(): Action
    {
        return Action::make('download_contract')
            ->label(__('filament-resources/maintenance-request.actions.download_contract', [], 'تحميل العقد'))
            ->color('primary')
            ->icon('heroicon-o-document-arrow-down')
            ->tooltip(__('filament-resources/maintenance-request.tooltips.download_contract', [], 'تحميل ملف العقد'))
            //->url(fn(): string => route('client.contract.download', $this->record->contract_id))
            ->openUrlInNewTab()
            ->visible(fn(): bool => $this->canPerformAction($this->record, 'download_contract'))
            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.download_contract', [], 'تحميل العقد')]);
    }

    /**
     * Build contact support action with enhanced form validation
     *
     * @return Action
     */
    protected function buildContactSupportAction(): Action
    {
        return Action::make('contact_support')
            ->label(__('filament-resources/maintenance-request.actions.contact_support', [], 'التواصل مع الدعم'))
            ->color('gray')
            ->icon('heroicon-o-phone')
            ->tooltip(__('filament-resources/maintenance-request.tooltips.contact_support', [], 'إرسال استفسار للدعم الفني'))
            ->form([
                Forms\Components\Select::make('inquiry_type')
                    ->label(__('filament-resources/maintenance-request.support.inquiry_type_label', [], 'نوع الاستفسار'))
                    ->options([
                        'status' => __('filament-resources/maintenance-request.support.inquiry_types.status', [], 'استفسار عن حالة الطلب'),
                        'payment' => __('filament-resources/maintenance-request.support.inquiry_types.payment', [], 'استفسار عن الدفع'),
                        'contract' => __('filament-resources/maintenance-request.support.inquiry_types.contract', [], 'استفسار عن العقد'),
                        'technical' => __('filament-resources/maintenance-request.support.inquiry_types.technical', [], 'استفسار تقني'),
                        'other' => __('filament-resources/maintenance-request.support.inquiry_types.other', [], 'أخرى'),
                    ])
                    ->required()
                    ->native(false)
                    ->placeholder(__('filament-resources/maintenance-request.support.inquiry_type_placeholder', [], 'اختر نوع الاستفسار')),

                Forms\Components\Textarea::make('message')
                    ->label(__('filament-resources/maintenance-request.support.message_label', [], 'رسالتك'))
                    ->required()
                    ->placeholder(__('filament-resources/maintenance-request.support.message_placeholder', [], 'اكتب استفسارك هنا...'))
                    ->rows(4)
                    ->minLength(10)
                    ->maxLength(1000)
                    ->helperText(__('filament-resources/maintenance-request.support.message_helper', [], 'الحد الأدنى 10 أحرف، الحد الأقصى 1000 حرف')),
            ])
            ->action(function (array $data): void {
                $this->handleSupportRequest($data);
            })
            ->extraAttributes(['aria-label' => __('filament-resources/maintenance-request.aria.contact_support', [], 'التواصل مع الدعم')]);
    }

    /**
     * Build cancel request action with enhanced confirmation
     *
     * @return Action
     */
    protected function buildCancelRequestAction(): Action
    {
        return Action::make('cancel_request')
            ->label(__('filament-resources/maintenance-request.actions.cancel_request', [], 'إلغاء الطلب'))
            ->color('danger')
            ->icon('heroicon-o-x-circle')
            ->tooltip(__('filament-resources/maintenance-request.tooltips.cancel_request', [], 'إلغاء الطلب نهائياً'))
            ->requiresConfirmation()
            ->modalHeading(__('filament-resources/maintenance-request.cancel.modal_heading', [], 'إلغاء الطلب'))
            ->modalDescription(__('filament-resources/maintenance-request.cancel.modal_description', [], 'هل أنت متأكد من رغبتك في إلغاء هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.'))
            ->modalSubmitActionLabel(__('filament-resources/maintenance-request.cancel.submit_label', [], 'نعم، إلغاء الطلب'))
            ->modalCancelActionLabel(__('filament-resources/maintenance-request.cancel.cancel_label', [], 'تراجع'))
            ->modalIcon('heroicon-o-exclamation-triangle')
            ->action(function (): void {
                $this->handleRequestCancellation();
            })
            ->visible(fn(): bool => $this->canPerformAction($this->record, 'cancel'))
            ->extraAttributes(['aria-label' => 'إلغاء الطلب']);
    }

    /**
     * Handle support request submission
     *
     * @param array $data
     * @return void
     */
    protected function handleSupportRequest(array $data): void
    {
        try {
            // Here you would typically send an email or create a support ticket
            // For now, we'll just log the request and show a success notification

            Log::info('Support request submitted', [
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id(),
                'inquiry_type' => $data['inquiry_type'],
                'message' => $data['message'],
                'timestamp' => now()
            ]);

            Notification::make()
                ->title(__('filament-resources/maintenance-request.notifications.support_sent_title', [], 'تم إرسال رسالتك'))
                ->body(__('filament-resources/maintenance-request.notifications.support_sent_body', [], 'سيتم التواصل معك خلال 24 ساعة عبر البريد الإلكتروني أو الهاتف'))
                ->success()
                ->duration(3000)
                ->send();

        } catch (Exception $e) {
            Log::error('Failed to submit support request', [
                'error' => $e->getMessage(),
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id()
            ]);

            Notification::make()
                ->title(__('filament-resources/maintenance-request.notifications.support_failed_title', [], 'فشل إرسال الرسالة'))
                ->body(__('filament-resources/maintenance-request.notifications.support_failed_body', [], 'حدث خطأ أثناء إرسال رسالتك. يرجى المحاولة مرة أخرى.'))
                ->danger()
                ->duration(5000)
                ->send();
        }
    }

    /**
     * Handle request cancellation with cache clearing
     *
     * @return void
     */
    protected function handleRequestCancellation(): void
    {
        try {
            $this->record->update(['status' => 'canceled']);

            // Clear cached data after status change
            $this->clearMaintenanceRequestCache($this->record);

            // Update local cached status
            $this->statusConfig = $this->getStatusConfig('canceled');

            Notification::make()
                ->title(__('filament-resources/maintenance-request.notifications.cancel_success_title', [], 'تم إلغاء الطلب'))
                ->body(__('filament-resources/maintenance-request.notifications.cancel_success_body', [], 'تم إلغاء طلب الصيانة بنجاح'))
                ->success()
                ->duration(3000)
                ->send();

        } catch (Exception $e) {
            Log::error('Failed to cancel maintenance request', [
                'error' => $e->getMessage(),
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id()
            ]);

            Notification::make()
                ->title(__('filament-resources/maintenance-request.notifications.cancel_failed_title', [], 'فشل إلغاء الطلب'))
                ->body(__('filament-resources/maintenance-request.notifications.cancel_failed_body', [], 'حدث خطأ أثناء إلغاء الطلب. يرجى المحاولة مرة أخرى.'))
                ->danger()
                ->duration(5000)
                ->send();
        }
    }
}
