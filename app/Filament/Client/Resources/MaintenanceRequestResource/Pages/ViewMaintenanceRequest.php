<?php

declare(strict_types=1);

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use App\Filament\Traits\HasMaintenanceRequestHelpers;
use App\Models\MaintenanceRequest;
use App\Services\DocumentTemplateService;
use App\Services\PdfStorageService;
use Exception;
use Filament\Actions\Action;
use Filament\Forms;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Log;

/**
 * Enhanced ViewMaintenanceRequest Page
 *
 * Provides a comprehensive view of maintenance requests with optimized performance,
 * improved user experience, and robust error handling. Features include:
 * - Cached data for improved performance
 * - Responsive design with accessibility support
 * - Enhanced PDF generation with loading states
 * - Comprehensive error handling and user feedback
 * - Modular, reusable component architecture
 *
 * @package App\Filament\Client\Resources\MaintenanceRequestResource\Pages
 * <AUTHOR>
 * @version 2.0.0
 */
class ViewMaintenanceRequest extends ViewRecord
{
    use HasMaintenanceRequestHelpers;

    protected static string $resource = MaintenanceRequestResource::class;

    protected static string $view = 'filament.client.resources.maintenance-contract-resource.pages.view-maintenance-request';

    /**
     * PDF URL for generated documents
     */
    public ?string $pdfUrl = null;

    /**
     * Loading state for PDF generation
     */
    public bool $isGeneratingPdf = false;

    /**
     * Cached status configuration
     */
    public array $statusConfig = [];

    /**
     * Cached payment summary
     */
    public array $paymentSummary = [];

    /**
     * Cache TTL in minutes
     */
    protected int $cacheMinutes = 5;


    /**
     * Get the page title compatible with Admin version
     *
     * @return string
     */
    public function getTitle(): string
    {
        return __('client/resources/maintenance_request.pages.view.title', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Get the page heading compatible with Admin version
     *
     * @return string
     */
    public function getHeading(): string
    {
        return __('client/resources/maintenance_request.pages.view.heading', [
            'number' => $this->getRecord()->request_number,
        ]);
    }

    /**
     * Mount the page with optimized data loading and caching
     *
     * @param int|string $record The record identifier
     * @return void
     */
    public function mount(int|string $record): void
    {
        parent::mount($record);

        $this->optimizeRecordLoading();
        $this->initializeCachedData();
    }

    /**
     * Optimize record loading with eager loading to prevent N+1 queries
     *
     * @return void
     */
    protected function optimizeRecordLoading(): void
    {
        // Eager load all necessary relationships to prevent N+1 queries
        $this->record->load([
            'client:id,name,phone,email,address',
            'contract:id,maintenance_request_id,contract_number,start_date,end_date,status,terms',
            'contractType:id,name,description,period,benefits',
            'assignedTechnician:id,name,phone,email',
            'payments:id,maintenance_request_id,amount,status,created_at'
        ]);
    }

    /**
     * Initialize cached data for improved performance
     *
     * @return void
     */
    protected function initializeCachedData(): void
    {
        // Cache status configuration
        $this->statusConfig = $this->getStatusConfig($this->record->status);

        // Cache payment summary
        $this->paymentSummary = $this->getPaymentSummary($this->record);
    }

    /**
     * Configure the enhanced infolist with optimized performance
     * Compatible with Admin version structure while maintaining client enhancements
     *
     * @param Infolist $infolist
     * @return Infolist
     */
    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Header Summary Cards - Client-specific enhancement
                $this->buildHeaderSummaryCards(),

                // Main Content Grid - Compatible with Admin 3-column structure
                $this->buildCompatibleMainContentGrid(),

                // Progress Timeline - Interactive and informative
                $this->buildProgressTimeline(),

                // Additional Information Tabs - Lazy loaded for performance
                $this->buildAdditionalInfoTabs(),
            ]);
    }

    /**
     * Build header summary cards with cached status configuration
     *
     * @return Grid
     */
    protected function buildHeaderSummaryCards(): Grid
    {
        if (empty($this->statusConfig)) {
            $this->initializeCachedData();
        }
        return Grid::make(4)
            ->schema([
                // Request Status Card
                Section::make(__('client/resources/maintenance_request.cards.status'))
                    ->schema([
                        TextEntry::make('status')
                            ->label('')
                            ->badge()
                            ->size(TextEntry\TextEntrySize::Large)
                            ->color($this->statusConfig['color'])
                            ->formatStateUsing(fn(): string => $this->statusConfig['text'])
                            ->extraAttributes([
                                'class' => 'status-badge-enhanced',
                                'aria-label' => __('client/resources/maintenance_request.aria.status_label') . ': ' . $this->statusConfig['text']
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),

                // Request Number Card
                Section::make(__('client/resources/maintenance_request.cards.request_number'))
                    ->schema([
                        TextEntry::make('request_number')
                            ->label('')
                            ->weight(FontWeight::Bold)
                            ->size(TextEntry\TextEntrySize::Large)
                            ->color('primary')
                            ->copyable()
                            ->copyMessage(__('client/resources/maintenance_request.messages.request_number_copied'))
                            ->copyMessageDuration(2000)
                            ->extraAttributes([
                                'aria-label' => __('client/resources/maintenance_request.aria.request_number_label') . ': ' . $this->record->request_number
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),

                // Contract Type Card
                Section::make(__('client/resources/maintenance_request.cards.contract_type'))
                    ->schema([
                        TextEntry::make('contractType.name')
                            ->label('')
                            ->weight(FontWeight::Bold)
                            ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified'))
                            ->extraAttributes([
                                'aria-label' => __('client/resources/maintenance_request.aria.contract_type_label') . ': ' . ($this->record->contractType?->name ?? __('client/resources/maintenance_request.placeholders.not_specified'))
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),

                // Creation Date Card
                Section::make(__('client/resources/maintenance_request.cards.creation_date'))
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('')
                            ->dateTime('d/m/Y H:i')
                            ->weight(FontWeight::Medium)
                            ->extraAttributes([
                                'aria-label' => __('client/resources/maintenance_request.aria.creation_date_label') . ': ' . $this->record->created_at->format('d/m/Y H:i')
                            ]),
                    ])
                    ->columnSpan(1)
                    ->extraAttributes(['class' => 'enhanced-card']),
            ])
            ->extraAttributes(['class' => 'header-summary-grid']);
    }

    /**
     * Build main content grid compatible with Admin version structure
     * Maintains 3-column layout while preserving client enhancements
     *
     * @return Grid
     */
    protected function buildCompatibleMainContentGrid(): Grid
    {
        return Grid::make(3)
            ->schema([
                // Request Details Column - Compatible with Admin structure
                $this->buildCompatibleRequestDetailsSection(),

                // Client & Contract Information Column - Merged for compatibility
                $this->buildCompatibleClientContractSection(),

                // Assignment & Financial Information Column - Admin-compatible
                $this->buildCompatibleAssignmentFinancialSection(),
            ])
            ->extraAttributes(['class' => 'main-content-grid']);
    }

    /**
     * Build main content grid with optimized data display (Legacy method)
     * Kept for backward compatibility
     *
     * @return Grid
     */
    protected function buildMainContentGrid(): Grid
    {
        return $this->buildCompatibleMainContentGrid();
    }

    /**
     * Build request details section compatible with Admin version
     * Includes title field and uses translation keys
     *
     * @return Section
     */
    protected function buildCompatibleRequestDetailsSection(): Section
    {
        return Section::make(__('client/resources/maintenance_request.sections.request_details'))
            ->schema([
                TextEntry::make('request_number')
                    ->label(__('client/resources/maintenance_request.fields.request_number'))
                    ->weight(FontWeight::Bold)
                    ->copyable()
                    ->copyMessage(__('client/resources/maintenance_request.messages.request_number_copied'))
                    ->copyMessageDuration(1500)
                    ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.request_number_label')]),

                TextEntry::make('title')
                    ->label(__('client/resources/maintenance_request.fields.title'))
                    ->weight(FontWeight::Medium)
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_title'))
                    ->extraAttributes(['aria-label' => 'عنوان الطلب']),

                TextEntry::make('created_at')
                    ->label(__('client/resources/maintenance_request.fields.created_at'))
                    ->dateTime('d/m/Y H:i')
                    ->extraAttributes(['aria-label' => 'تاريخ إنشاء الطلب']),

                TextEntry::make('status')
                    ->label(__('client/resources/maintenance_request.fields.status'))
                    ->badge()
                    ->color($this->statusConfig['color'])
                    ->formatStateUsing(fn(): string => $this->statusConfig['text'])
                    ->extraAttributes(['aria-label' => 'حالة الطلب']),

                TextEntry::make('visits_included')
                    ->label(__('client/resources/maintenance_request.fields.visits_included'))
                    ->badge()
                    ->color('info')
                    ->extraAttributes(['aria-label' => 'عدد الزيارات المشمولة']),

                TextEntry::make('notes')
                    ->label(__('client/resources/maintenance_request.fields.notes'))
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_notes'))
                    ->markdown()
                    ->limit(150)
                    ->tooltip(fn(?string $state): ?string => $state && strlen($state) > 150 ? $state : null)
                    ->extraAttributes(['aria-label' => 'ملاحظات الطلب']),
            ])
            ->columnSpan(1)
            ->extraAttributes(['class' => 'enhanced-card request-details']);
    }

    /**
     * Build request details section (Legacy method)
     * Kept for backward compatibility
     *
     * @return Section
     */
    protected function buildRequestDetailsSection(): Section
    {
        return $this->buildCompatibleRequestDetailsSection();
    }

    /**
     * Build client & contract information section compatible with Admin version
     * Combines client and contract info to match Admin 3-column layout
     *
     * @return Section
     */
    protected function buildCompatibleClientContractSection(): Section
    {
        return Section::make(__('client/resources/maintenance_request.sections.client_contract'))
            ->schema([
                TextEntry::make('client.name')
                    ->label(__('client/resources/maintenance_request.fields.client_id'))
                    ->weight(FontWeight::Bold)
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified'))
                    ->url(fn($record) => $record->client_id
                        ? MaintenanceRequestResource::getUrl('index', ['tableFilters' => ['client_id' => ['value' => $record->client_id]]])
                        : null)
                    ->extraAttributes(['aria-label' => 'اسم العميل']),

                TextEntry::make('client.phone')
                    ->label(__('client/resources/maintenance_request.fields.client_phone'))
                    ->icon('heroicon-o-phone')
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified'))
                    ->copyable()
                    ->copyMessage(__('client/resources/maintenance_request.messages.phone_copied'))
                    ->url(fn(?string $state): ?string => $state ? "tel:{$state}" : null)
                    ->extraAttributes(['aria-label' => 'رقم الهاتف']),

                TextEntry::make('client.email')
                    ->label(__('client/resources/maintenance_request.fields.client_email'))
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified'))
                    ->copyable()
                    ->copyMessage(__('client/resources/maintenance_request.messages.email_copied'))
                    ->url(fn(?string $state): ?string => $state ? "mailto:{$state}" : null)
                    ->extraAttributes(['aria-label' => 'البريد الإلكتروني']),

                TextEntry::make('contract.contract_number')
                    ->label(__('client/resources/maintenance_request.fields.contract_number'))
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_contract'))
                    ->copyable()
                    ->copyMessage(__('client/resources/maintenance_request.messages.contract_number_copied'))
                    ->extraAttributes(['aria-label' => 'رقم العقد']),

                TextEntry::make('contractType.name')
                    ->label(__('client/resources/maintenance_request.fields.contract_type'))
                    ->weight(FontWeight::Bold)
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_specified'))
                    ->extraAttributes(['aria-label' => 'نوع العقد']),

                TextEntry::make('contract.status')
                    ->label(__('client/resources/maintenance_request.fields.contract_status'))
                    ->badge()
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_contract'))
                    ->color(fn(?string $state): string => match ($state) {
                        'pending' => 'warning',
                        'active' => 'success',
                        'expired' => 'danger',
                        'terminated' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(?string $state): string => $this->getContractStatusText($state))
                    ->extraAttributes(['aria-label' => 'حالة العقد']),
            ])
            ->columnSpan(1)
            ->extraAttributes(['class' => 'enhanced-card client-contract-info']);
    }

    /**
     * Build client information section (Legacy method)
     * Kept for backward compatibility
     *
     * @return Section
     */
    protected function buildClientInformationSection(): Section
    {
        return $this->buildCompatibleClientContractSection();
    }

    /**
     * Build assignment & financial information section compatible with Admin version
     * Includes assigned technician and price fields like Admin version
     *
     * @return Section
     */
    protected function buildCompatibleAssignmentFinancialSection(): Section
    {
        return Section::make(__('client/resources/maintenance_request.sections.assignment_financial'))
            ->schema([
                TextEntry::make('assignedTechnician.name')
                    ->label(__('client/resources/maintenance_request.fields.assigned_technician'))
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_assigned'))
                    ->weight(FontWeight::Medium)
                    ->extraAttributes(['aria-label' => 'الفني المكلف']),

                TextEntry::make('price')
                    ->label(__('client/resources/maintenance_request.fields.price'))
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_priced'))
                    ->formatStateUsing(fn(?float $state): string => $state ? number_format($state, 2) . ' ريال' : 'لم يتم تحديد السعر بعد')
                    ->color('success')
                    ->weight(FontWeight::Bold)
                    ->extraAttributes(['aria-label' => 'السعر']),

                TextEntry::make('payment_status')
                    ->label(__('client/resources/maintenance_request.fields.payment_status'))
                    ->state(fn(): string => $this->paymentSummary['status'] ?? __('client/resources/maintenance_request.placeholders.no_payments'))
                    ->badge()
                    ->color(fn(): string => $this->paymentSummary['status_color'] ?? 'gray')
                    ->extraAttributes(['aria-label' => 'حالة الدفع']),

                TextEntry::make('contractType.period')
                    ->label(__('client/resources/maintenance_request.fields.contract_period'))
                    ->formatStateUsing(fn(?int $state): string => $state ? $state . ' ' . __('client/resources/maintenance_request.units.months') : __('client/resources/maintenance_request.placeholders.not_specified'))
                    ->extraAttributes(['aria-label' => 'مدة العقد']),

                TextEntry::make('contract.start_date')
                    ->label(__('client/resources/maintenance_request.fields.contract_start_date'))
                    ->date('d/m/Y')
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_set'))
                    ->extraAttributes(['aria-label' => 'تاريخ بداية العقد']),

                TextEntry::make('contract.end_date')
                    ->label(__('client/resources/maintenance_request.fields.contract_end_date'))
                    ->date('d/m/Y')
                    ->placeholder(__('client/resources/maintenance_request.placeholders.not_set'))
                    ->extraAttributes(['aria-label' => 'تاريخ انتهاء العقد']),
            ])
            ->columnSpan(1)
            ->extraAttributes(['class' => 'enhanced-card assignment-financial']);
    }

    /**
     * Build contract and financial information section (Legacy method)
     * Kept for backward compatibility
     *
     * @return Section
     */
    protected function buildContractFinancialSection(): Section
    {
        return $this->buildCompatibleAssignmentFinancialSection();
    }

    /**
     * Build enhanced progress timeline
     *
     * @return Section
     */
    protected function buildProgressTimeline(): Section
    {
        return Section::make(__('client/resources/maintenance_request.sections.progress_timeline'))
            ->schema([
                TextEntry::make('progress_timeline')
                    ->label('')
                    ->state(function (): string {
                        return $this->generateTimelineContent();
                    })
                    ->markdown()
                    ->columnSpanFull()
                    ->extraAttributes([
                        'class' => 'timeline-container',
                        'aria-label' => __('client/resources/maintenance_request.aria.progress_timeline')
                    ]),
            ])
            ->collapsible()
            ->collapsed(false)
            ->extraAttributes(['class' => 'enhanced-card timeline-section']);
    }

    /**
     * Generate timeline content based on request status
     *
     * @return string
     */
    protected function generateTimelineContent(): string
    {
        $timeline = [];
        $status = $this->record->status;

        // Step 1: Request Created
        $timeline[] = '✅ **' . __('client/resources/maintenance_request.timeline.request_created') . '** - ' . $this->record->created_at->format('d/m/Y H:i');

        // Step 2: Under Review
        if (in_array($status, ['assigned', 'in_progress', 'completed'])) {
            $timeline[] = '✅ **' . __('client/resources/maintenance_request.timeline.under_review') . '** - ' . __('client/resources/maintenance_request.timeline.review_completed');
        } elseif ($status === 'pending') {
            $timeline[] = '⏳ **' . __('client/resources/maintenance_request.timeline.under_review') . '** - ' . __('client/resources/maintenance_request.timeline.awaiting_review');
        } else {
            $timeline[] = '⏸️ **' . __('client/resources/maintenance_request.timeline.under_review') . '** - ' . __('client/resources/maintenance_request.timeline.awaiting_review');
        }

        // Step 3: Technician Assignment
        if (in_array($status, ['assigned', 'in_progress', 'completed'])) {
            $technicianName = $this->record->assignedTechnician?->name ?? __('client/resources/maintenance_request.timeline.technician_assigned');
            $timeline[] = '✅ **' . __('client/resources/maintenance_request.timeline.technician_assigned') . '** - ' . $technicianName;
        } elseif ($status === 'pending') {
            $timeline[] = '⏳ **' . __('client/resources/maintenance_request.timeline.awaiting_assignment') . '**';
        } else {
            $timeline[] = '⏸️ **' . __('client/resources/maintenance_request.timeline.awaiting_assignment') . '**';
        }

        // Step 4: Work in Progress
        if (in_array($status, ['in_progress', 'completed'])) {
            $timeline[] = '✅ **' . __('client/resources/maintenance_request.timeline.work_in_progress') . '**';
        } elseif ($status === 'assigned') {
            $timeline[] = '⏳ **' . __('client/resources/maintenance_request.timeline.awaiting_work_start') . '**';
        } else {
            $timeline[] = '⏸️ **' . __('client/resources/maintenance_request.timeline.awaiting_work_start') . '**';
        }

        // Step 5: Contract Creation (Optional)
        if ($this->record->contract) {
            $contractNumber = $this->record->contract->contract_number;
            $timeline[] = "✅ **" . __('client/resources/maintenance_request.timeline.contract_created') . "** - " . __('client/resources/maintenance_request.timeline.contract_number') . ": {$contractNumber}";
        } elseif (in_array($status, ['assigned', 'in_progress'])) {
            $timeline[] = '⏳ **' . __('client/resources/maintenance_request.timeline.awaiting_contract_creation') . '**';
        } else {
            $timeline[] = '⏸️ **' . __('client/resources/maintenance_request.timeline.contract_creation') . '** - ' . __('client/resources/maintenance_request.timeline.awaiting_assignment');
        }

        // Step 6: Completion
        if ($status === 'completed') {
            $timeline[] = '✅ **' . __('client/resources/maintenance_request.timeline.processing_completed') . '**';
        } else {
            $timeline[] = '⏳ **' . __('client/resources/maintenance_request.timeline.processing_completion') . '** - ' . __('client/resources/maintenance_request.timeline.awaiting_final_procedures');
        }

        return implode("\n\n", $timeline);
    }

    /**
     * Build additional information tabs with lazy loading
     *
     * @return Tabs
     */
    protected function buildAdditionalInfoTabs(): Tabs
    {
        return Tabs::make(__('client/resources/maintenance_request.sections.additional_info'))
            ->tabs([
                $this->buildNotesDetailsTab(),
                $this->buildPaymentsTab(),
                $this->buildContractTab(),
            ])
            ->columnSpanFull()
            ->persistTabInQueryString()
            ->extraAttributes(['class' => 'enhanced-tabs']);
    }

    /**
     * Build notes and details tab
     *
     * @return Tabs\Tab
     */
    protected function buildNotesDetailsTab(): Tabs\Tab
    {
        return Tabs\Tab::make(__('client/resources/maintenance_request.tabs.notes_details'))
            ->schema([
                TextEntry::make('notes')
                    ->label(__('client/resources/maintenance_request.fields.notes'))
                    ->markdown()
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_notes_available'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.notes')]),

                TextEntry::make('contractType.description')
                    ->label(__('client/resources/maintenance_request.fields.contract_type_description'))
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_description_available'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_type_description')]),

                TextEntry::make('contractType.benefits')
                    ->label(__('client/resources/maintenance_request.fields.contract_benefits'))
                    ->state(function (): string {
                        $benefits = $this->record->contractType?->benefits ?? [];
                        return is_array($benefits) && !empty($benefits)
                            ? implode("\n• ", [''] + $benefits)
                            : __('client/resources/maintenance_request.placeholders.no_benefits_specified');
                    })
                    ->markdown()
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_benefits')]),
            ])
            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.notes_details_tab')]);
    }

    /**
     * Build payments tab with cached data
     *
     * @return Tabs\Tab
     */
    protected function buildPaymentsTab(): Tabs\Tab
    {
        return Tabs\Tab::make(__('client/resources/maintenance_request.tabs.payments'))
            ->schema([
                Grid::make(3)
                    ->schema([
                        TextEntry::make('payments_total')
                            ->label(__('client/resources/maintenance_request.fields.total_amount_required'))
                            ->state(fn(): string => $this->paymentSummary['total_amount'] > 0
                                ? number_format($this->paymentSummary['total_amount'], 2) . ' ' . __('client/resources/maintenance_request.units.riyal')
                                : __('client/resources/maintenance_request.placeholders.not_set_yet')
                            )
                            ->color('primary')
                            ->weight(FontWeight::Bold)
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.total_amount_required')]),

                        TextEntry::make('payments_paid')
                            ->label(__('client/resources/maintenance_request.fields.amount_paid'))
                            ->state(fn(): string => number_format($this->paymentSummary['paid_amount'] ?? 0, 2) . ' ' . __('client/resources/maintenance_request.units.riyal')
                            )
                            ->color('success')
                            ->weight(FontWeight::Bold)
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.amount_paid')]),

                        TextEntry::make('payments_pending')
                            ->label(__('client/resources/maintenance_request.fields.amount_remaining'))
                            ->state(fn(): string => number_format($this->paymentSummary['pending_amount'] ?? 0, 2) . ' ' . __('client/resources/maintenance_request.units.riyal')
                            )
                            ->color('warning')
                            ->weight(FontWeight::Bold)
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.amount_remaining')]),
                    ]),

                TextEntry::make('payment_instructions')
                    ->label(__('client/resources/maintenance_request.fields.payment_instructions'))
                    ->state(__('client/resources/maintenance_request.messages.payment_instructions'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.payment_instructions')]),
            ])
            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.payments_tab')]);
    }

    /**
     * Build contract tab
     *
     * @return Tabs\Tab
     */
    protected function buildContractTab(): Tabs\Tab
    {
        return Tabs\Tab::make(__('client/resources/maintenance_request.tabs.linked_contract'))
            ->schema([
                Section::make(__('client/resources/maintenance_request.sections.contract_details'))
                    ->schema([
                        TextEntry::make('contract.contract_number')
                            ->label(__('client/resources/maintenance_request.fields.contract_number'))
                            ->placeholder(__('client/resources/maintenance_request.placeholders.no_contract_created'))
                            ->copyable()
                            ->copyMessage(__('client/resources/maintenance_request.messages.contract_number_copied'))
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_number')]),

                        TextEntry::make('contract.start_date')
                            ->label(__('client/resources/maintenance_request.fields.start_date'))
                            ->date('d/m/Y')
                            ->placeholder(__('client/resources/maintenance_request.placeholders.not_set'))
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_start_date')]),

                        TextEntry::make('contract.end_date')
                            ->label(__('client/resources/maintenance_request.fields.end_date'))
                            ->date('d/m/Y')
                            ->placeholder(__('client/resources/maintenance_request.placeholders.not_set'))
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_end_date')]),

                        TextEntry::make('contract.status')
                            ->label(__('client/resources/maintenance_request.fields.contract_status'))
                            ->badge()
                            ->placeholder(__('client/resources/maintenance_request.placeholders.no_contract'))
                            ->color(fn(?string $state): string => match ($state) {
                                'pending' => 'warning',
                                'active' => 'success',
                                'expired' => 'danger',
                                'terminated' => 'danger',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn(?string $state): string => $this->getContractStatusText($state))
                            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_status')]),
                    ])
                    ->columns(4),

                TextEntry::make('contract.terms')
                    ->label(__('client/resources/maintenance_request.fields.contract_terms'))
                    ->markdown()
                    ->placeholder(__('client/resources/maintenance_request.placeholders.no_terms_set'))
                    ->columnSpanFull()
                    ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contract_terms')]),
            ])
            ->visible(fn(): bool => $this->record->contract_id !== null)
            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.linked_contract_tab')]);
    }

    /**
     * Public method to generate PDF for the current maintenance request
     *
     * @return void
     */
    public function generatePdf(): void
    {
        // Use the trait method for PDF generation
        $this->generateMaintenanceRequestPdf($this->record);
    }

    /**
     * Generate PDF specifically for modal display with persistent storage
     *
     * @return void
     */
    public function generatePdfForModal(): void
    {
        try {
            // Check if a valid PDF already exists and doesn't need regeneration
            if (!$this->record->needsPdfRegeneration()) {
                $existingUrl = PdfStorageService::getPdfUrl($this->record);
                if ($existingUrl) {
                    $this->pdfUrl = $existingUrl;

                    $this->sendPdfLoadedNotification();

                    Log::info('Using existing PDF for modal display', [
                        'maintenance_request_id' => $this->record->id,
                        'pdf_url' => $existingUrl,
                        'pdf_generated_at' => $this->record->pdf_generated_at
                    ]);

                    return;
                }
            }

            // Use the trait method for PDF generation with storage
            $this->generateMaintenanceRequestPdf($this->record);

            // Send appropriate notification based on the result
            if (!empty($this->pdfUrl)) {
                // Check if PDF was stored persistently or is temporary
                if ($this->record->hasPdf()) {
                    $this->sendPdfGeneratedNotification();
                } else {
                    $this->sendPdfTemporaryNotification();
                }
            }

        } catch (Exception $e) {
            Log::error('PDF generation failed in modal', [
                'error' => $e->getMessage(),
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->sendPdfErrorNotification();
        }
    }



    /**
     * Send notification for loaded existing PDF
     *
     * @return void
     */
    protected function sendPdfLoadedNotification(): void
    {
        Notification::make()
            ->title(__('client/resources/maintenance_request.notifications.pdf_loaded'))
            ->body(__('client/resources/maintenance_request.notifications.pdf_loaded_body'))
            ->success()
            ->duration(2000)
            ->send();
    }

    /**
     * Send notification for successfully generated and stored PDF
     *
     * @return void
     */
    protected function sendPdfGeneratedNotification(): void
    {
        Notification::make()
            ->title(__('client/resources/maintenance_request.notifications.pdf_generated'))
            ->body(__('client/resources/maintenance_request.notifications.pdf_generated_body'))
            ->success()
            ->duration(3000)
            ->send();
    }

    /**
     * Send notification for temporary PDF (storage failed)
     *
     * @return void
     */
    protected function sendPdfTemporaryNotification(): void
    {
        Notification::make()
            ->title(__('client/resources/maintenance_request.notifications.pdf_generated_temp'))
            ->body(__('client/resources/maintenance_request.notifications.pdf_storage_failed'))
            ->warning()
            ->duration(4000)
            ->send();
    }

    /**
     * Send notification for PDF generation error
     *
     * @return void
     */
    protected function sendPdfErrorNotification(): void
    {
        Notification::make()
            ->title(__('client/resources/maintenance_request.notifications.pdf_generation_failed'))
            ->body(__('client/resources/maintenance_request.notifications.pdf_generation_failed_body'))
            ->danger()
            ->duration(5000)
            ->send();
    }

    /**
     * Build PDF modal content with viewer
     *
     * @return View
     */
    protected function buildPdfModalContent(): View
    {
        $this->generatePdfForModal();
        return view('filament.components.pdf-modal-content', [
            'pdfUrl' => $this->pdfUrl,
            'isGenerating' => $this->isGeneratingPdf,
            'record' => $this->record,
            'isPersistent' => $this->record->hasPdf(),
            'fileSize' => $this->record->getPdfFileSizeFormatted(),
            'generatedAt' => $this->record->pdf_generated_at,
        ]);
    }

    /**
     * Get enhanced header actions with improved UX and accessibility
     *
     * @return array<Action>
     */
    protected function getHeaderActions(): array
    {
        return [
            //$this->buildPrintPdfAction(),
            $this->buildDownloadContractAction(),
            $this->buildContactSupportAction(),
            $this->buildCancelRequestAction(),
        ];
    }

    /**
     * Build generate PDF action with modal viewer
     *
     * @return Action
     */
    protected function buildPrintPdfAction(): Action
    {
        return Action::make('print_pdf')
            ->label(__('client/resources/maintenance_request.actions.print_request'))
            ->color('warning')
            ->icon('heroicon-o-document-arrow-down')
            ->tooltip(__('client/resources/maintenance_request.tooltips.print_request'))
            ->disabled(fn(): bool => $this->isGeneratingPdf)
            ->modal()
            ->modalHeading(__('client/resources/maintenance_request.actions.print_request'))
            ->modalDescription(__('client/resources/maintenance_request.tooltips.print_request'))
            ->modalWidth('7xl')
            ->modalContent(function (): View {
                return $this->buildPdfModalContent();
            })
            ->slideOver()
            ->modalActions([
                Action::make('download')
                    ->label(__('client/resources/maintenance_request.actions.download_pdf'))
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(fn(): ?string => $this->pdfUrl)
                    ->openUrlInNewTab()
                    ->visible(fn(): bool => !empty($this->pdfUrl)),
                Action::make('close')
                    ->label(__('client/resources/maintenance_request.actions.close'))
                    ->color('gray')
                    ->modalCancelAction(),
            ])

            ->visible(fn(): bool => $this->canPerformAction($this->record, 'print_pdf'))
            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.generate_pdf')]);
    }

    /**
     * Build download contract action
     *
     * @return Action
     */
    protected function buildDownloadContractAction(): Action
    {
        return Action::make('download_contract')
            ->label(__('client/resources/maintenance_request.actions.download_contract'))
            ->color('primary')
            ->icon('heroicon-o-document-arrow-down')
            ->tooltip(__('client/resources/maintenance_request.tooltips.download_contract'))
            //->url(fn(): string => route('client.contract.download', $this->record->contract_id))
            ->openUrlInNewTab()
            ->visible(fn(): bool => $this->canPerformAction($this->record, 'download_contract'))
            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.download_contract')]);
    }

    /**
     * Build contact support action with enhanced form validation
     *
     * @return Action
     */
    protected function buildContactSupportAction(): Action
    {
        return Action::make('contact_support')
            ->label(__('client/resources/maintenance_request.actions.contact_support'))
            ->color('gray')
            ->icon('heroicon-o-phone')
            ->tooltip(__('client/resources/maintenance_request.tooltips.contact_support'))
            ->form([
                Forms\Components\Select::make('inquiry_type')
                    ->label(__('client/resources/maintenance_request.support.inquiry_type_label'))
                    ->options([
                        'status' => __('client/resources/maintenance_request.support.inquiry_types.status'),
                        'payment' => __('client/resources/maintenance_request.support.inquiry_types.payment'),
                        'contract' => __('client/resources/maintenance_request.support.inquiry_types.contract'),
                        'technical' => __('client/resources/maintenance_request.support.inquiry_types.technical'),
                        'other' => __('client/resources/maintenance_request.support.inquiry_types.other'),
                    ])
                    ->required()
                    ->native(false)
                    ->placeholder(__('client/resources/maintenance_request.support.inquiry_type_placeholder')),

                Forms\Components\Textarea::make('message')
                    ->label(__('client/resources/maintenance_request.support.message_label'))
                    ->required()
                    ->placeholder(__('client/resources/maintenance_request.support.message_placeholder'))
                    ->rows(4)
                    ->minLength(10)
                    ->maxLength(1000)
                    ->helperText(__('client/resources/maintenance_request.support.message_helper')),
            ])
            ->action(function (array $data): void {
                $this->handleSupportRequest($data);
            })
            ->extraAttributes(['aria-label' => __('client/resources/maintenance_request.aria.contact_support')]);
    }

    /**
     * Build cancel request action with enhanced confirmation
     *
     * @return Action
     */
    protected function buildCancelRequestAction(): Action
    {
        return Action::make('cancel_request')
            ->label(__('client/resources/maintenance_request.actions.cancel_request'))
            ->color('danger')
            ->icon('heroicon-o-x-circle')
            ->tooltip(__('client/resources/maintenance_request.tooltips.cancel_request'))
            ->requiresConfirmation()
            ->modalHeading(__('client/resources/maintenance_request.cancel.modal_heading'))
            ->modalDescription(__('client/resources/maintenance_request.cancel.modal_description'))
            ->modalSubmitActionLabel(__('client/resources/maintenance_request.cancel.submit_label'))
            ->modalCancelActionLabel(__('client/resources/maintenance_request.cancel.cancel_label'))
            ->modalIcon('heroicon-o-exclamation-triangle')
            ->action(function (): void {
                $this->handleRequestCancellation();
            })
            ->visible(fn(): bool => $this->canPerformAction($this->record, 'cancel'))
            ->extraAttributes(['aria-label' => 'إلغاء الطلب']);
    }

    /**
     * Handle support request submission
     *
     * @param array $data
     * @return void
     */
    protected function handleSupportRequest(array $data): void
    {
        try {
            // Here you would typically send an email or create a support ticket
            // For now, we'll just log the request and show a success notification

            Log::info('Support request submitted', [
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id(),
                'inquiry_type' => $data['inquiry_type'],
                'message' => $data['message'],
                'timestamp' => now()
            ]);

            Notification::make()
                ->title(__('client/resources/maintenance_request.notifications.support_sent_title'))
                ->body(__('client/resources/maintenance_request.notifications.support_sent_body'))
                ->success()
                ->duration(3000)
                ->send();

        } catch (Exception $e) {
            Log::error('Failed to submit support request', [
                'error' => $e->getMessage(),
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id()
            ]);

            Notification::make()
                ->title(__('client/resources/maintenance_request.notifications.support_failed_title'))
                ->body(__('client/resources/maintenance_request.notifications.support_failed_body'))
                ->danger()
                ->duration(5000)
                ->send();
        }
    }

    /**
     * Handle request cancellation with cache clearing
     *
     * @return void
     */
    protected function handleRequestCancellation(): void
    {
        try {
            $this->record->update(['status' => 'canceled']);

            // Clear cached data after status change
            $this->clearMaintenanceRequestCache($this->record);

            // Update local cached status
            $this->statusConfig = $this->getStatusConfig('canceled');

            Notification::make()
                ->title(__('client/resources/maintenance_request.notifications.cancel_success_title'))
                ->body(__('client/resources/maintenance_request.notifications.cancel_success_body'))
                ->success()
                ->duration(3000)
                ->send();

        } catch (Exception $e) {
            Log::error('Failed to cancel maintenance request', [
                'error' => $e->getMessage(),
                'maintenance_request_id' => $this->record->id,
                'user_id' => auth()->id()
            ]);

            Notification::make()
                ->title(__('client/resources/maintenance_request.notifications.cancel_failed_title'))
                ->body(__('client/resources/maintenance_request.notifications.cancel_failed_body'))
                ->danger()
                ->duration(5000)
                ->send();
        }
    }
}
