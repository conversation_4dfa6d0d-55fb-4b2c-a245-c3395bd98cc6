<?php

namespace App\Filament\Client\Resources\MaintenanceRequestResource\Pages;

use App\Filament\Client\Resources\MaintenanceRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

class ListMaintenanceRequests extends ListRecords
{
    protected static string $resource = MaintenanceRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('new_contract')
                ->label(__('client/resources/maintenance_request.actions.new_contract'))
                ->icon('heroicon-o-plus')
                ->url(route('filament.client.resources.maintenance-contracts.wizard.step1')),
        ];
    }

    protected function getTableQuery(): Builder
    {
        // Get current client ID
        $clientId = Auth::guard('client')->id();

        // Only show contracts belonging to the current client
        return parent::getTableQuery()
            ->where('client_id', $clientId);
    }
}
