<?php

declare(strict_types=1);

namespace App\Filament\Traits;

use App\Models\MaintenanceRequest;
use App\Models\Client;
use App\Models\Contract;
use App\Models\User;
use Filament\Forms;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\FontWeight;

/**
 * Trait HasMaintenanceRequestTableHelpers
 *
 * Provides reusable table components and utilities for maintenance request tables.
 * Features include:
 * - Optimized column definitions with eager loading
 * - Advanced filtering with cached options
 * - Responsive column visibility
 * - Enhanced accessibility with ARIA labels
 * - Performance-optimized queries
 *
 * @package App\Filament\Traits
 * <AUTHOR>
 * @version 2.0.0
 */
trait HasMaintenanceRequestTableHelpers
{
    /**
     * Cache TTL for table options (in minutes).
     */
    protected static int $tableCacheMinutes = 10;

    /**
     * Get optimized table columns with eager loading.
     *
     * @return array<Column>
     */
    protected static function getTableColumns(): array
    {
        return [
            static::getRequestNumberColumn(),
            static::getClientNameColumn(),
            static::getContractNumberColumn(),
            static::getTitleColumn(),
            static::getStatusColumn(),
            static::getRequestDateColumn(),
            static::getAssignedTechnicianColumn(),
            static::getCompletionDateColumn(),
            static::getCreatedAtColumn(),
        ];
    }

    /**
     * Get the request number column.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getRequestNumberColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('request_number')
            ->label(__('filament-resources/maintenance-request.columns.request_number'))
            ->searchable()
            ->sortable()
            ->weight(FontWeight::Bold)
            ->copyable()
            ->copyMessage(__('filament-resources/maintenance-request.messages.request_number_copied'))
            ->extraAttributes([
                'class' => 'font-mono',
                'dir' => 'ltr',
            ]);
    }

    /**
     * Get the client name column with relationship optimization.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getClientNameColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('client.name')
            ->label(__('filament-resources/maintenance-request.columns.client.name'))
            ->searchable(['clients.name', 'clients.phone'])
            ->sortable()
            ->description(fn (MaintenanceRequest $record): ?string => $record->client?->phone)
            ->tooltip(fn (MaintenanceRequest $record): ?string =>
                $record->client ? "Phone: {$record->client->phone}" : null
            );
    }

    /**
     * Get the contract number column.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getContractNumberColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('contract.contract_number')
            ->label(__('filament-resources/maintenance-request.columns.contract.contract_number'))
            ->searchable(['contracts.contract_number'])
            ->sortable()
            ->placeholder(__('filament-resources/maintenance-request.placeholders.no_contract'))
            ->extraAttributes([
                'class' => 'font-mono',
                'dir' => 'ltr',
            ]);
    }

    /**
     * Get the title column with truncation.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getTitleColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('title')
            ->label(__('filament-resources/maintenance-request.columns.title'))
            ->searchable()
            ->limit(30)
            ->tooltip(fn (MaintenanceRequest $record): ?string => $record->title)
            ->toggleable();
    }



    /**
     * Get the status column with color coding.
     *
     * @return Tables\Columns\BadgeColumn
     */
    protected static function getStatusColumn(): Tables\Columns\BadgeColumn
    {
        return Tables\Columns\BadgeColumn::make('status')
            ->label(__('filament-resources/maintenance-request.columns.status'))
            ->colors([
                'primary' => MaintenanceRequest::STATUS_NEW,
                'secondary' => MaintenanceRequest::STATUS_ASSIGNED,
                'warning' => MaintenanceRequest::STATUS_IN_PROGRESS,
                'success' => MaintenanceRequest::STATUS_COMPLETED,
                'danger' => MaintenanceRequest::STATUS_CANCELLED,
            ])
            ->formatStateUsing(fn (string $state): string =>
                __("filament-resources/maintenance-request.status_options.{$state}")
            )
            ->sortable();
    }

    /**
     * Get the request date column.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getRequestDateColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('request_date')
            ->label(__('filament-resources/maintenance-request.columns.request_date'))
            ->date()
            ->sortable()
            ->toggleable();
    }

    /**
     * Get the assigned technician column.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getAssignedTechnicianColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('assignedTechnician.name')
            ->label(__('filament-resources/maintenance-request.columns.assignedTechnician.name'))
            ->placeholder(__('filament-resources/maintenance-request.placeholders.unassigned'))
            ->sortable()
            ->toggleable();
    }

    /**
     * Get the completion date column.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getCompletionDateColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('completion_date')
            ->label(__('filament-resources/maintenance-request.columns.completion_date'))
            ->date()
            ->sortable()
            ->toggleable(isToggledHiddenByDefault: true);
    }

    /**
     * Get the created at column.
     *
     * @return Tables\Columns\TextColumn
     */
    protected static function getCreatedAtColumn(): Tables\Columns\TextColumn
    {
        return Tables\Columns\TextColumn::make('created_at')
            ->label(__('filament-resources/maintenance-request.columns.created_at'))
            ->dateTime()
            ->sortable()
            ->toggleable(isToggledHiddenByDefault: true);
    }

    /**
     * Get optimized table filters with caching.
     *
     * @return array<Filter>
     */
    protected static function getTableFilters(): array
    {
        return [
            static::getClientFilter(),
            static::getContractFilter(),
            static::getStatusFilter(),
            static::getAssignedTechnicianFilter(),
            static::getDateRangeFilter(),
        ];
    }

    /**
     * Get the client filter with cached options.
     *
     * @return Tables\Filters\SelectFilter
     */
    protected static function getClientFilter(): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make('client_id')
            ->label(__('filament-resources/maintenance-request.filters.client_id'))
            ->relationship('client', 'name')
            ->searchable()
            ->preload()
            ->multiple();
    }

    /**
     * Get the contract filter.
     *
     * @return Tables\Filters\SelectFilter
     */
    protected static function getContractFilter(): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make('contract_id')
            ->label(__('filament-resources/maintenance-request.filters.contract_id'))
            ->relationship('contract', 'contract_number')
            ->searchable()
            ->preload();
    }



    /**
     * Get the status filter.
     *
     * @return Tables\Filters\SelectFilter
     */
    protected static function getStatusFilter(): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make('status')
            ->label(__('filament-resources/maintenance-request.filters.status'))
            ->options([
                MaintenanceRequest::STATUS_NEW => __('filament-resources/maintenance-request.status_options.new'),
                MaintenanceRequest::STATUS_ASSIGNED => __('filament-resources/maintenance-request.status_options.assigned'),
                MaintenanceRequest::STATUS_IN_PROGRESS => __('filament-resources/maintenance-request.status_options.in_progress'),
                MaintenanceRequest::STATUS_COMPLETED => __('filament-resources/maintenance-request.status_options.completed'),
                MaintenanceRequest::STATUS_CANCELLED => __('filament-resources/maintenance-request.status_options.canceled'),
            ])
            ->multiple();
    }

    /**
     * Get the assigned technician filter.
     *
     * @return Tables\Filters\SelectFilter
     */
    protected static function getAssignedTechnicianFilter(): Tables\Filters\SelectFilter
    {
        return Tables\Filters\SelectFilter::make('assigned_to')
            ->label(__('filament-resources/maintenance-request.filters.assigned_to'))
            ->relationship('assignedTechnician', 'name')
            ->multiple();
    }

    /**
     * Get the date range filter.
     *
     * @return Filter
     */
    protected static function getDateRangeFilter(): Filter
    {
        return Filter::make('created_at')
            ->form([
                Forms\Components\DatePicker::make('created_from')
                    ->label(__('filament-resources/maintenance-request.filters.created_from'))
                    ->placeholder(__('filament-resources/maintenance-request.filters.created_from_placeholder')),
                Forms\Components\DatePicker::make('created_until')
                    ->label(__('filament-resources/maintenance-request.filters.created_until'))
                    ->placeholder(__('filament-resources/maintenance-request.filters.created_until_placeholder')),
            ])
            ->query(function (Builder $query, array $data): Builder {
                return $query
                    ->when(
                        $data['created_from'],
                        fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                    )
                    ->when(
                        $data['created_until'],
                        fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                    );
            })
            ->indicateUsing(function (array $data): array {
                $indicators = [];

                if ($data['created_from']) {
                    $indicators[] = Tables\Filters\Indicator::make(__('filament-resources/maintenance-request.filters.created_from') . ': ' . $data['created_from'])
                        ->removeField('created_from');
                }

                if ($data['created_until']) {
                    $indicators[] = Tables\Filters\Indicator::make(__('filament-resources/maintenance-request.filters.created_until') . ': ' . $data['created_until'])
                        ->removeField('created_until');
                }

                return $indicators;
            });
    }

    /**
     * Get enhanced bulk actions for maintenance requests.
     *
     * @return array<Action>
     */
    protected static function getBulkActions(): array
    {
        return [
            Tables\Actions\BulkAction::make('assign_technician')
                ->label(__('filament-resources/maintenance-request.bulk_actions.assign_technician'))
                ->icon('heroicon-o-user-plus')
                ->color('primary')
                ->form([
                    Forms\Components\Select::make('assigned_to')
                        ->label(__('filament-resources/maintenance-request.fields.assigned_to'))
                        ->options(function (): array {
                            $cacheKey = 'technicians_active_' . (tenant()->id ?? 'default');

                            return Cache::remember($cacheKey, now()->addMinutes(static::$tableCacheMinutes), function () {
                                return User::where('role', 'technician')
                                    //->where('is_active', true)
                                    ->orderBy('name')
                                    ->pluck('name', 'id')
                                    ->toArray();
                            });
                        })
                        ->required()
                        ->searchable(),
                ])
                ->action(function (Collection $records, array $data): void {
                    $records->each(function (MaintenanceRequest $record) use ($data) {
                        $record->update([
                            'assigned_to' => $data['assigned_to'],
                            'status' => $record->status === 'new' ? 'assigned' : $record->status,
                        ]);
                    });

                    Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.bulk_assigned'))
                        ->success()
                        ->send();
                })
                ->deselectRecordsAfterCompletion(),

            Tables\Actions\BulkAction::make('update_status')
                ->label(__('filament-resources/maintenance-request.bulk_actions.update_status'))
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->form([
                    Forms\Components\Select::make('status')
                        ->label(__('filament-resources/maintenance-request.fields.status'))
                        ->options([
                            MaintenanceRequest::STATUS_NEW => __('filament-resources/maintenance-request.status_options.new'),
                            MaintenanceRequest::STATUS_ASSIGNED => __('filament-resources/maintenance-request.status_options.assigned'),
                            MaintenanceRequest::STATUS_IN_PROGRESS => __('filament-resources/maintenance-request.status_options.in_progress'),
                            MaintenanceRequest::STATUS_COMPLETED => __('filament-resources/maintenance-request.status_options.completed'),
                            MaintenanceRequest::STATUS_CANCELLED => __('filament-resources/maintenance-request.status_options.canceled'),
                        ])
                        ->required(),
                ])
                ->action(function (Collection $records, array $data): void {
                    $records->each(function (MaintenanceRequest $record) use ($data) {
                        $updateData = ['status' => $data['status']];

                        if ($data['status'] === MaintenanceRequest::STATUS_COMPLETED && !$record->completed_at) {
                            $updateData['completed_at'] = now();
                        }

                        $record->update($updateData);
                    });

                    Notification::make()
                        ->title(__('filament-resources/maintenance-request.notifications.bulk_status_updated'))
                        ->success()
                        ->send();
                })
                ->deselectRecordsAfterCompletion(),

            Tables\Actions\DeleteBulkAction::make()
                ->label(__('filament-resources/maintenance-request.bulk_actions.delete'))
                ->requiresConfirmation(),
        ];
    }

    /**
     * Get optimized table query with eager loading.
     *
     * @return Builder
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with([
                'client:id,name,phone,email',
                'contract:id,maintenance_request_id,contract_number,maintenance_request_id',
                'contractType:id,name',
                'assignedTechnician:id,name',
            ])
            ->withCount(['payments'])
            ->latest();
    }

    /**
     * Clear table-related caches.
     *
     * @return void
     */
    public static function clearTableCaches(): void
    {
        $tenantId = tenant()->id ?? 'default';

        Cache::forget("technicians_active_{$tenantId}");
        Cache::forget("clients_active_{$tenantId}");
        Cache::forget("contracts_active_{$tenantId}");
    }
}
