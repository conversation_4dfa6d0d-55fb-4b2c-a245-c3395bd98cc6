<?php

declare(strict_types=1);

namespace App\Filament\Traits;

use App\Models\MaintenanceRequest;
use App\Models\Payment;
use App\Services\DocumentTemplateService;
use App\Services\PdfStorageService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Joaopaulolndev\FilamentPdfViewer\Infolists\Components\PdfViewerEntry;

/**
 * Trait HasMaintenanceRequestHelpers
 *
 * Provides common functionality for maintenance request pages including:
 * - Status handling and configuration
 * - Payment summary calculations with caching
 * - PDF generation with error handling
 * - Cache management utilities
 * - Permission checking methods
 *
 * @package App\Filament\Traits
 * <AUTHOR>
 * @version 2.0.0
 */
trait HasMaintenanceRequestHelpers
{
    /**
     * Cache TTL for maintenance request data (5 minutes)
     */
    protected int $cacheMinutes = 5;

    /**
     * Get status configuration for a maintenance request
     *
     * @param string $status The maintenance request status
     * @return array Status configuration with color, icon, text, etc.
     */
    protected function getStatusConfig(string $status): array
    {
        return match ($status) {
            'new' => [
                'color' => 'warning',
                'icon' => 'heroicon-o-clock',
                'text' => __('filament-resources/maintenance-request.status_options.new'),
                'bg' => 'bg-yellow-50 dark:bg-yellow-900/20',
                'text_color' => 'text-yellow-800 dark:text-yellow-200',
                'border' => 'border-yellow-200 dark:border-yellow-700'
            ],
            'pending' => [
                'color' => 'info',
                'icon' => 'heroicon-o-eye',
                'text' => 'قيد المراجعة',
                'bg' => 'bg-blue-50 dark:bg-blue-900/20',
                'text_color' => 'text-blue-800 dark:text-blue-200',
                'border' => 'border-blue-200 dark:border-blue-700'
            ],
            'approved' => [
                'color' => 'success',
                'icon' => 'heroicon-o-check-circle',
                'text' => 'تمت الموافقة',
                'bg' => 'bg-green-50 dark:bg-green-900/20',
                'text_color' => 'text-green-800 dark:text-green-200',
                'border' => 'border-green-200 dark:border-green-700'
            ],
            'assigned' => [
                'color' => 'primary',
                'icon' => 'heroicon-o-user-plus',
                'text' => 'تم التعيين',
                'bg' => 'bg-blue-50 dark:bg-blue-900/20',
                'text_color' => 'text-blue-800 dark:text-blue-200',
                'border' => 'border-blue-200 dark:border-blue-700'
            ],
            'in_progress' => [
                'color' => 'warning',
                'icon' => 'heroicon-o-cog-6-tooth',
                'text' => 'قيد التنفيذ',
                'bg' => 'bg-amber-50 dark:bg-amber-900/20',
                'text_color' => 'text-amber-800 dark:text-amber-200',
                'border' => 'border-amber-200 dark:border-amber-700'
            ],
            'rejected' => [
                'color' => 'danger',
                'icon' => 'heroicon-o-x-circle',
                'text' => 'مرفوض',
                'bg' => 'bg-red-50 dark:bg-red-900/20',
                'text_color' => 'text-red-800 dark:text-red-200',
                'border' => 'border-red-200 dark:border-red-700'
            ],
            'completed' => [
                'color' => 'success',
                'icon' => 'heroicon-o-check-badge',
                'text' => 'مكتمل',
                'bg' => 'bg-green-50 dark:bg-green-900/20',
                'text_color' => 'text-green-800 dark:text-green-200',
                'border' => 'border-green-200 dark:border-green-700'
            ],
            'canceled' => [
                'color' => 'gray',
                'icon' => 'heroicon-o-x-mark',
                'text' => 'ملغي',
                'bg' => 'bg-gray-50 dark:bg-gray-900/20',
                'text_color' => 'text-gray-800 dark:text-gray-200',
                'border' => 'border-gray-200 dark:border-gray-700'
            ],
            default => [
                'color' => 'gray',
                'icon' => 'heroicon-o-question-mark-circle',
                'text' => $status,
                'bg' => 'bg-gray-50 dark:bg-gray-900/20',
                'text_color' => 'text-gray-800 dark:text-gray-200',
                'border' => 'border-gray-200 dark:border-gray-700'
            ]
        };
    }

    /**
     * Get contract status in Arabic
     *
     * @param string|null $status The contract status
     * @return string Arabic translation of the status
     */
    protected function getContractStatusText(?string $status): string
    {
        return match ($status) {
            'pending' => 'في الانتظار',
            'active' => 'نشط',
            'expired' => 'منتهي الصلاحية',
            'terminated' => 'ملغي',
            default => 'في الانتظار'
        };
    }

    /**
     * Get payment summary for a maintenance request with caching
     *
     * @param MaintenanceRequest $record The maintenance request record
     * @return array Payment summary with totals and status
     */
    protected function getPaymentSummary(MaintenanceRequest $record): array
    {
        $cacheKey = "maintenance_request_payments_{$record->id}";

        return Cache::remember($cacheKey, now()->addMinutes($this->cacheMinutes), function () use ($record) {
            $payments = Payment::where('maintenance_request_id', $record->id)->get();

            $totalPaid = $payments->where('status', 'completed')->sum('amount');
            $totalAmount = $payments->sum('amount');
            $pendingAmount = $payments->where('status', 'pending')->sum('amount');

            $paymentStatus = match (true) {
                $totalAmount == 0 => 'لا توجد مدفوعات',
                $totalPaid == 0 => 'غير مدفوع',
                $totalPaid < $totalAmount => 'مدفوع جزئياً',
                default => 'مدفوع بالكامل'
            };

            return [
                'total_amount' => $totalAmount,
                'paid_amount' => $totalPaid,
                'pending_amount' => $pendingAmount,
                'status' => $paymentStatus,
                'status_color' => match ($paymentStatus) {
                    'لا توجد مدفوعات' => 'gray',
                    'غير مدفوع' => 'danger',
                    'مدفوع جزئياً' => 'warning',
                    'مدفوع بالكامل' => 'success',
                    default => 'gray'
                }
            ];
        });
    }

    /**
     * Prepare PDF data for maintenance request
     *
     * @param MaintenanceRequest $record The maintenance request record
     * @return array Formatted data for PDF generation
     */
    protected function preparePdfData(MaintenanceRequest $record): array
    {
        // Ensure relationships are loaded
        $record->loadMissing(['client', 'contract', 'contractType', 'assignedTechnician']);

        return [
            'maintenanceRequest' => [
                'id' => $record->request_number,
                'title' => 'طلب صيانة - ' . ($record->contractType?->name ?? 'غير محدد'),
                'description' => $record->notes ?? 'لا توجد ملاحظات',
                'status' => $this->getStatusConfig($record->status)['text'],
                'priority' => 'عادية',
                'created_at' => $record->created_at->format('Y-m-d H:i:s'),
                'scheduled_at' => $record->created_at->addDays(2)->format('Y-m-d H:i:s'),
                'visits_included' => $record->visits_included,
            ],
            'customer' => [
                'name' => $record->client?->name ?? 'غير محدد',
                'company' => $record->client?->company ?? 'غير محدد',
                'phone' => $record->client?->phone ?? 'غير محدد',
                'email' => $record->client?->email ?? 'غير محدد',
                'address' => $record->client?->address ?? 'غير محدد',
            ],
            'contract' => [
                'number' => $record->contract?->contract_number ?? 'لم يتم إنشاء العقد بعد',
                'type' => $record->contractType?->name ?? 'غير محدد',
                'start_date' => $record->contract?->start_date?->format('Y-m-d') ?? 'لم يتم تحديده',
                'end_date' => $record->contract?->end_date?->format('Y-m-d') ?? 'لم يتم تحديده',
                'status' => $this->getContractStatusText($record->contract?->status),
            ],
            'technician' => [
                'name' => $record->assignedTechnician?->name ?? 'لم يتم التعيين بعد',
                'phone' => $record->assignedTechnician?->phone ?? 'غير محدد',
                'email' => $record->assignedTechnician?->email ?? 'غير محدد',
            ],
            'company' => [
                'name' => config('app.company_name', 'شركة الصيانة'),
                'address' => config('app.company_address', 'الرياض، المملكة العربية السعودية'),
                'phone' => config('app.company_phone', '+966123456789'),
                'email' => config('app.company_email', '<EMAIL>'),
                'logo' => config('app.company_logo', '/images/logo.png'),
            ],
        ];
    }

    /**
     * Generate PDF for maintenance request with comprehensive error handling and storage
     *
     * @param MaintenanceRequest $record The maintenance request record
     * @return void
     */
    protected function generateMaintenanceRequestPdf(MaintenanceRequest $record): void
    {
        try {
            // Set loading state
            $this->setLoadingState(true);

            // Check if valid PDF already exists and doesn't need regeneration
            if (!$record->needsPdfRegeneration()) {
                $existingUrl = PdfStorageService::getPdfUrl($record);
                if ($existingUrl) {
                    $this->setPdfUrl($existingUrl);
                    $this->renderPdfViewer($existingUrl);
                    return;
                }
            }

            // Generate new PDF
            $pdfUrl = $this->generateNewPdf($record);

            if ($pdfUrl) {
                $this->setPdfUrl($pdfUrl);
                $this->renderPdfViewer($pdfUrl);
            } else {
                $this->handlePdfGenerationError();
            }

        } catch (\Exception $e) {
            Log::error('PDF generation failed for maintenance request', [
                'maintenance_request_id' => $record->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id()
            ]);

            $this->handlePdfGenerationError();
        } finally {
            $this->setLoadingState(false);
        }
    }

    /**
     * Generate new PDF and attempt to store it persistently
     *
     * @param MaintenanceRequest $record
     * @return string|null The PDF URL or null if generation failed
     */
    protected function generateNewPdf(MaintenanceRequest $record): ?string
    {
        // Prepare data for PDF generation
        $pdfData = $this->preparePdfData($record);

        // Generate PDF using DocumentTemplateService
        $result = DocumentTemplateService::generatePdf('MaintenanceRequestPrint', $pdfData);

        if (!$result || !isset($result['url']) || $result['outcome'] !== 'SUCCESS') {
            return null;
        }

        // Attempt to store PDF persistently
        $persistentUrl = $this->attemptPdfStorage($record, $result['url']);

        // Return persistent URL if successful, otherwise temporary URL
        return $persistentUrl ?? $result['url'];
    }

    /**
     * Attempt to store PDF persistently and return the storage URL
     *
     * @param MaintenanceRequest $record
     * @param string $temporaryUrl
     * @return string|null The persistent storage URL or null if storage failed
     */
    protected function attemptPdfStorage(MaintenanceRequest $record, string $temporaryUrl): ?string
    {
        try {
            // Fetch PDF content from temporary URL
            $pdfContent = $this->fetchPdfContent($temporaryUrl);

            if (!$pdfContent) {
                Log::warning('Failed to fetch PDF content for storage', [
                    'maintenance_request_id' => $record->id,
                    'temp_url' => $temporaryUrl
                ]);
                return null;
            }

            // Store PDF using PdfStorageService
            $storageResult = PdfStorageService::storePdf($record, $pdfContent);

            if ($storageResult['success']) {
                Log::info('PDF generated and stored successfully', [
                    'maintenance_request_id' => $record->id,
                    'pdf_url' => $storageResult['url'],
                    'pdf_path' => $storageResult['path'],
                    'file_size' => strlen($pdfContent)
                ]);

                return $storageResult['url'];
            } else {
                Log::warning('PDF storage failed, using temporary URL', [
                    'maintenance_request_id' => $record->id,
                    'storage_error' => $storageResult['error'],
                    'temp_url' => $temporaryUrl
                ]);

                return null;
            }

        } catch (\Exception $e) {
            Log::error('Error during PDF storage attempt', [
                'maintenance_request_id' => $record->id,
                'error' => $e->getMessage(),
                'temp_url' => $temporaryUrl
            ]);

            return null;
        }
    }

    /**
     * Set PDF URL property if it exists
     *
     * @param string $url
     * @return void
     */
    protected function setPdfUrl(string $url): void
    {
        if (property_exists($this, 'pdfUrl')) {
            $this->pdfUrl = $url;
        }
    }

    /**
     * Set loading state property if it exists
     *
     * @param bool $isLoading
     * @return void
     */
    protected function setLoadingState(bool $isLoading): void
    {
        if (property_exists($this, 'isGeneratingPdf')) {
            $this->isGeneratingPdf = $isLoading;
        }
    }

    /**
     * Render PDF viewer component
     *
     * @param string $url
     * @return void
     */
    protected function renderPdfViewer(string $url): void
    {
        PdfViewerEntry::make('file')
            ->hiddenLabel()
            ->minHeight('80svh')
            ->fileUrl($url)
            ->columnSpanFull();
    }

    /**
     * Fetch PDF content from a URL with validation
     *
     * @param string $url The PDF URL to fetch content from
     * @return string|null The PDF content or null if failed
     */
    protected function fetchPdfContent(string $url): ?string
    {
        try {
            // Create HTTP context with timeout and headers
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Laravel PDF Fetcher/1.0',
                        'Accept: application/pdf',
                        'Cache-Control: no-cache'
                    ],
                    'ignore_errors' => true,
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                ],
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                ],
            ]);

            // Fetch content from URL
            $content = file_get_contents($url, false, $context);

            if ($content === false) {
                Log::warning('Failed to fetch PDF content from URL', [
                    'url' => $url,
                    'http_response_header' => $http_response_header ?? []
                ]);
                return null;
            }

            // Validate PDF content
            if (!$this->isValidPdfContent($content)) {
                Log::warning('Fetched content is not a valid PDF', [
                    'url' => $url,
                    'content_length' => strlen($content),
                    'content_start' => substr($content, 0, 20)
                ]);
                return null;
            }

            Log::debug('PDF content fetched successfully', [
                'url' => $url,
                'content_length' => strlen($content)
            ]);

            return $content;

        } catch (\Exception $e) {
            Log::error('Error fetching PDF content', [
                'url' => $url,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode()
            ]);
            return null;
        }
    }

    /**
     * Validate that content is a valid PDF
     *
     * @param string $content
     * @return bool
     */
    protected function isValidPdfContent(string $content): bool
    {
        // Check PDF header
        if (strpos($content, '%PDF-') !== 0) {
            return false;
        }

        // Check minimum content length (basic PDF should be at least 100 bytes)
        if (strlen($content) < 100) {
            return false;
        }

        // Check for PDF trailer (optional but good validation)
        if (strpos($content, '%%EOF') === false) {
            Log::warning('PDF content missing EOF marker - may be incomplete');
        }

        return true;
    }

    /**
     * Handle PDF generation errors with user-friendly messages
     *
     * @param array|null $result The PDF generation result from DocumentTemplateService
     * @return void
     */
    protected function handlePdfGenerationError(?array $result = null): void
    {
        // Default error message
        $errorMessage = __('filament-resources/maintenance-request.messages.pdf_generation_default_error', [], 'تأكد من صحة إعدادات DocKing ومحتوى القالب');

        // Use specific error message if available
        if ($result && isset($result['message']) && !empty($result['message'])) {
            $errorMessage = $result['message'];
        }

        // Send error notification
        Notification::make()
            ->title(__('filament-resources/maintenance-request.notifications.pdf_generation_failed', [], 'فشل إنشاء ملف PDF'))
            ->body($errorMessage)
            ->danger()
            ->duration(5000)
            ->send();
    }

    /**
     * Clear cached data for maintenance request
     *
     * @param MaintenanceRequest $record The maintenance request record
     * @return void
     */
    protected function clearMaintenanceRequestCache(MaintenanceRequest $record): void
    {
        $cacheKeys = [
            "maintenance_request_payments_{$record->id}",
            "maintenance_request_summary_{$record->id}",
            "maintenance_request_status_{$record->id}",
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Check if user can perform action on maintenance request
     *
     * @param MaintenanceRequest $record The maintenance request record
     * @param string $action The action to check
     * @return bool Whether the action is allowed
     */
    protected function canPerformAction(MaintenanceRequest $record, string $action): bool
    {
        return match ($action) {
            'cancel' => in_array($record->status, ['new', 'pending', 'approved']),
            'edit' => in_array($record->status, ['new', 'pending']),
            'assign_technician' => in_array($record->status, ['approved']),
            'start_work' => in_array($record->status, ['assigned']),
            'complete_work' => in_array($record->status, ['in_progress']),
            'download_contract' => $record->contract_id && $record->contract?->status === 'active',
            'print_pdf' => !in_array($record->status, ['canceled', 'rejected']),
            'preview' => true,
            'contact_support' => true,
            default => false
        };
    }

    /**
     * Get formatted timeline steps for maintenance request
     *
     * @param MaintenanceRequest $record The maintenance request record
     * @return array Array of timeline steps with status and description
     */
    protected function getTimelineSteps(MaintenanceRequest $record): array
    {
        $status = $record->status;
        $steps = [];

        // Step 1: Request Created
        $steps[] = [
            'status' => 'completed',
            'icon' => '✅',
            'title' => 'تم إنشاء الطلب',
            'description' => $record->created_at->format('d/m/Y H:i'),
            'completed' => true
        ];

        // Step 2: Under Review
        $steps[] = [
            'status' => in_array($status, ['pending', 'approved', 'assigned', 'in_progress', 'completed']) ? 'completed' : 'pending',
            'icon' => in_array($status, ['pending', 'approved', 'assigned', 'in_progress', 'completed']) ? '✅' : '⏳',
            'title' => 'قيد المراجعة',
            'description' => in_array($status, ['pending', 'approved', 'assigned', 'in_progress', 'completed']) ? 'تمت مراجعة الطلب' : 'في انتظار المراجعة',
            'completed' => in_array($status, ['pending', 'approved', 'assigned', 'in_progress', 'completed'])
        ];

        // Step 3: Approved/Rejected
        if (in_array($status, ['approved', 'assigned', 'in_progress', 'completed'])) {
            $steps[] = [
                'status' => 'completed',
                'icon' => '✅',
                'title' => 'تمت الموافقة',
                'description' => 'تم اعتماد الطلب',
                'completed' => true
            ];
        } elseif ($status === 'rejected') {
            $steps[] = [
                'status' => 'rejected',
                'icon' => '❌',
                'title' => 'تم رفض الطلب',
                'description' => 'لم يتم اعتماد الطلب',
                'completed' => true
            ];
        } else {
            $steps[] = [
                'status' => 'pending',
                'icon' => '⏳',
                'title' => 'في انتظار قرار الموافقة',
                'description' => 'قيد المراجعة من قبل الإدارة',
                'completed' => false
            ];
        }

        // Step 4: Technician Assignment
        if (in_array($status, ['assigned', 'in_progress', 'completed'])) {
            $steps[] = [
                'status' => 'completed',
                'icon' => '✅',
                'title' => 'تم تعيين الفني',
                'description' => $record->assignedTechnician ? 'الفني: ' . $record->assignedTechnician->name : 'تم تعيين فني للمهمة',
                'completed' => true
            ];
        } elseif ($status === 'approved') {
            $steps[] = [
                'status' => 'pending',
                'icon' => '⏳',
                'title' => 'في انتظار تعيين الفني',
                'description' => 'سيتم تعيين فني قريباً',
                'completed' => false
            ];
        } else {
            $steps[] = [
                'status' => 'waiting',
                'icon' => '⏸️',
                'title' => 'تعيين الفني',
                'description' => 'في انتظار الموافقة',
                'completed' => false
            ];
        }

        // Step 5: Work in Progress
        if (in_array($status, ['in_progress', 'completed'])) {
            $steps[] = [
                'status' => 'completed',
                'icon' => '✅',
                'title' => 'بدء العمل',
                'description' => 'الفني يعمل على المهمة',
                'completed' => true
            ];
        } elseif ($status === 'assigned') {
            $steps[] = [
                'status' => 'pending',
                'icon' => '⏳',
                'title' => 'في انتظار بدء العمل',
                'description' => 'الفني سيبدأ العمل قريباً',
                'completed' => false
            ];
        } else {
            $steps[] = [
                'status' => 'waiting',
                'icon' => '⏸️',
                'title' => 'بدء العمل',
                'description' => 'في انتظار تعيين الفني',
                'completed' => false
            ];
        }

        // Step 6: Contract Creation (Optional)
        if ($record->contract_id) {
            $steps[] = [
                'status' => 'completed',
                'icon' => '✅',
                'title' => 'تم إنشاء العقد',
                'description' => 'رقم العقد: ' . $record->contract->contract_number,
                'completed' => true
            ];
        } elseif (in_array($status, ['approved', 'assigned', 'in_progress'])) {
            $steps[] = [
                'status' => 'pending',
                'icon' => '⏳',
                'title' => 'في انتظار إنشاء العقد',
                'description' => 'سيتم إنشاء العقد قريباً',
                'completed' => false
            ];
        } else {
            $steps[] = [
                'status' => 'waiting',
                'icon' => '⏸️',
                'title' => 'إنشاء العقد',
                'description' => 'في انتظار الموافقة',
                'completed' => false
            ];
        }

        // Step 7: Completion
        $steps[] = [
            'status' => $status === 'completed' ? 'completed' : 'pending',
            'icon' => $status === 'completed' ? '✅' : '⏳',
            'title' => $status === 'completed' ? 'تم اكتمال الصيانة' : 'اكتمال الصيانة',
            'description' => $status === 'completed' ? 'تم إنجاز جميع أعمال الصيانة بنجاح' : 'في انتظار إنهاء أعمال الصيانة',
            'completed' => $status === 'completed'
        ];

        return $steps;
    }

    /**
     * Get maintenance request statistics for dashboard
     *
     * @param int|null $clientId Optional client ID to filter by
     * @return array Statistics array
     */
    protected function getMaintenanceRequestStats(?int $clientId = null): array
    {
        $query = MaintenanceRequest::query();

        if ($clientId) {
            $query->where('client_id', $clientId);
        }

        return [
            'total' => $query->count(),
            'new' => $query->where('status', 'new')->count(),
            'pending' => $query->where('status', 'pending')->count(),
            'approved' => $query->where('status', 'approved')->count(),
            'assigned' => $query->where('status', 'assigned')->count(),
            'in_progress' => $query->where('status', 'in_progress')->count(),
            'completed' => $query->where('status', 'completed')->count(),
            'rejected' => $query->where('status', 'rejected')->count(),
            'canceled' => $query->where('status', 'canceled')->count(),
        ];
    }
}
