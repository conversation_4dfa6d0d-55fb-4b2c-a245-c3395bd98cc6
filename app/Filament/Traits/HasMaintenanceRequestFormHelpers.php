<?php

declare(strict_types=1);

namespace App\Filament\Traits;

use App\Models\MaintenanceRequest;
use App\Models\ContractType;
use App\Models\Client;
use App\Models\User;
use App\Models\Contract;
use Filament\Forms;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;

/**
 * Trait HasMaintenanceRequestFormHelpers
 *
 * Provides reusable form components and utilities for maintenance request forms.
 * Features include:
 * - Cached dropdown options for improved performance
 * - Reactive form fields with auto-population
 * - Comprehensive validation rules
 * - Accessibility enhancements with ARIA labels
 * - RTL language support
 *
 * @package App\Filament\Traits
 * <AUTHOR>
 * @version 2.0.0
 */
trait HasMaintenanceRequestFormHelpers
{
    /**
     * Cache TTL for form options (in minutes).
     */
    protected static int $formCacheMinutes = 15;

    /**
     * Get the request number field with auto-generation.
     *
     * @return Forms\Components\TextInput
     */
    protected static function getRequestNumberField(): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make('request_number')
            ->label(__('filament-resources/maintenance-request.fields.request_number'))
            ->required()
            ->maxLength(255)
            ->unique(ignoreRecord: true)
            ->disabled(fn (string $operation): bool => $operation === 'edit')
            ->dehydrated()
            ->default(function () {
                return static::generateRequestNumber();
            })
            ->helperText(__('filament-resources/maintenance-request.fields.request_number_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.request_number'),
                'dir' => 'ltr',
            ]);
    }

    /**
     * Get the contract type field with caching and reactivity.
     *
     * @return Forms\Components\Select
     */
    protected static function getContractTypeField(): Forms\Components\Select
    {
        return Forms\Components\Select::make('contract_type_id')
            ->label(__('filament-resources/maintenance-request.fields.contract_type_id'))
            ->options(function (): array {
                $cacheKey = 'contract_types_active_' . tenant()->id ?? 'default';

                return Cache::remember($cacheKey, now()->addMinutes(static::$formCacheMinutes), function () {
                    return ContractType::where('is_active', true)
                        ->orderBy('display_order')
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray();
                });
            })
            ->required()
            ->searchable()
            ->preload()
            ->live()
            ->disabled(fn (string $operation): bool => in_array($operation, ['edit', 'view']))
            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                if ($state) {
                    static::handleContractTypeChange($set, $get, $state);
                }
            })
            ->helperText(__('filament-resources/maintenance-request.fields.contract_type_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.contract_type_id'),
            ]);
    }

    /**
     * Get the client field with caching and search functionality.
     *
     * @return Forms\Components\Select
     */
    protected static function getClientField(): Forms\Components\Select
    {
        return Forms\Components\Select::make('client_id')
            ->label(__('filament-resources/maintenance-request.fields.client_id'))
            ->relationship('client', 'name')
            ->required()
            ->searchable(['name', 'phone', 'email'])
            ->preload()
            ->getSearchResultsUsing(function (string $search): array {
                return Client::where('is_active', true)
                    ->where(function (Builder $query) use ($search) {
                        $query->where('name', 'like', "%{$search}%")
                            ->orWhere('phone', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    })
                    ->limit(50)
                    ->pluck('name', 'id')
                    ->toArray();
            })
            ->getOptionLabelUsing(function ($value): ?string {
                $client = Client::find($value);
                return $client ? "{$client->name} ({$client->phone})" : null;
            })
            ->disabled(fn (string $operation): bool => in_array($operation, ['edit', 'view']))
            ->helperText(__('filament-resources/maintenance-request.fields.client_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.client_id'),
            ]);
    }

    /**
     * Get the assigned technician field with role filtering.
     *
     * @return Forms\Components\Select
     */
    protected static function getAssignedTechnicianField(): Forms\Components\Select
    {
        return Forms\Components\Select::make('assigned_to')
            ->label(__('filament-resources/maintenance-request.fields.assigned_to'))
            ->options(function (): array {
                $cacheKey = 'technicians_active_' . tenant()->id ?? 'default';

                return Cache::remember($cacheKey, now()->addMinutes(static::$formCacheMinutes), function () {
                    return User::where('role', 'technician')
                        //->where('is_active', true)
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray();
                });
            })
            ->searchable()
            ->preload()
            ->nullable()
            ->helperText(__('filament-resources/maintenance-request.fields.assigned_to_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.assigned_to'),
            ]);
    }

    /**
     * Get the completion date field with validation.
     *
     * @return Forms\Components\DatePicker
     */
    protected static function getCompletionDateField(): Forms\Components\DatePicker
    {
        return Forms\Components\DatePicker::make('completion_date')
            ->label(__('filament-resources/maintenance-request.fields.completion_date'))
            ->nullable()
            ->minDate(now())
            ->helperText(__('filament-resources/maintenance-request.fields.completion_date_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.completion_date'),
            ]);
    }

    /**
     * Get the notes field with enhanced text area.
     *
     * @return Forms\Components\Textarea
     */
    protected static function getNotesField(): Forms\Components\Textarea
    {
        return Forms\Components\Textarea::make('notes')
            ->label(__('filament-resources/maintenance-request.fields.notes'))
            ->rows(4)
            ->maxLength(1000)
            ->columnSpanFull()
            ->helperText(__('filament-resources/maintenance-request.fields.notes_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.notes'),
                'dir' => app()->getLocale() === 'ar' ? 'rtl' : 'ltr',
            ]);
    }



    /**
     * Get the status field with conditional options.
     *
     * @return Forms\Components\Select
     */
    protected static function getStatusField(): Forms\Components\Select
    {
        return Forms\Components\Select::make('status')
            ->label(__('filament-resources/maintenance-request.fields.status'))
            ->options([
                MaintenanceRequest::STATUS_NEW => __('filament-resources/maintenance-request.status_options.new'),
                MaintenanceRequest::STATUS_ASSIGNED => __('filament-resources/maintenance-request.status_options.assigned'),
                MaintenanceRequest::STATUS_IN_PROGRESS => __('filament-resources/maintenance-request.status_options.in_progress'),
                MaintenanceRequest::STATUS_COMPLETED => __('filament-resources/maintenance-request.status_options.completed'),
                MaintenanceRequest::STATUS_CANCELLED => __('filament-resources/maintenance-request.status_options.canceled'),
            ])
            ->default(MaintenanceRequest::STATUS_NEW)
            ->required()
            ->disabled(fn (string $operation): bool => $operation === 'create')
            ->helperText(__('filament-resources/maintenance-request.fields.status_help'))
            ->extraAttributes([
                'aria-label' => __('filament-resources/maintenance-request.fields.status'),
            ]);
    }

    /**
     * Generate a unique request number.
     *
     * @return string The generated request number
     */
    protected static function generateRequestNumber(): string
    {
        $cacheKey = 'last_maintenance_request_id';

        $lastId = Cache::remember($cacheKey, now()->addMinutes(1), function () {
            return MaintenanceRequest::latest('id')->value('id') ?? 0;
        });

        $nextId = $lastId + 1;

        return 'REQ-' . date('Ym') . '-' . str_pad((string) $nextId, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Handle contract type change and auto-populate related fields.
     *
     * @param Set $set The form setter
     * @param Get $get The form getter
     * @param mixed $contractTypeId The selected contract type ID
     * @return void
     */
    protected static function handleContractTypeChange(Set $set, Get $get, $contractTypeId): void
    {
        if (!$contractTypeId) {
            return;
        }

        try {
            $contractType = ContractType::find($contractTypeId);

            if ($contractType) {
                // Auto-populate visits included if available
                if (isset($contractType->visit_limit)) {
                    $set('visits_included', $contractType->visit_limit);
                }

                // Set default completion date based on contract type period
                if (isset($contractType->period)) {
                    $completionDate = now()->addMonths($contractType->period);
                    $set('completion_date', $completionDate->format('Y-m-d'));
                }
            }
        } catch (\Exception $e) {
            Log::error('Error handling contract type change: ' . $e->getMessage());
        }
    }

    /**
     * Clear form-related caches.
     *
     * @return void
     */
    public static function clearFormCaches(): void
    {
        $tenantId = tenant()->id ?? 'default';

        Cache::forget("contract_types_active_{$tenantId}");
        Cache::forget("technicians_active_{$tenantId}");
        Cache::forget('last_maintenance_request_id');
    }
}
