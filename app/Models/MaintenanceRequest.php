<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class MaintenanceRequest extends Model
{
    /**
     * Status constants for maintenance requests.
     */
    public const STATUS_NEW = 'new';
    public const STATUS_ASSIGNED = 'assigned';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_CANCELLED = 'canceled';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'request_number',
        'client_id',
        'contract_type_id',
        'assigned_to',
        'created_by_id',
        'created_by_type',
        'title',
        'description',
        'notes',
        'price',
        'status',
        'visits_included',
        'request_date',
        'completion_date',
        'completed_at',
        'pdf_file_path',
        'pdf_disk',
        'pdf_generated_at',
    ];

    /**
     * Get all available status options.
     *
     * @return array
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_NEW => __('maintenance.status.new'),
            self::STATUS_ASSIGNED => __('maintenance.status.assigned'),
            self::STATUS_IN_PROGRESS => __('maintenance.status.in_progress'),
            self::STATUS_COMPLETED => __('maintenance.status.completed'),
            self::STATUS_CANCELLED => __('maintenance.status.canceled'),
        ];
    }

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'request_date' => 'date',
        'completion_date' => 'date',
        'completed_at' => 'datetime',
        'pdf_generated_at' => 'datetime',
    ];

    public function contract()
    {
        return $this->hasOne(Contract::class);
    }

    public function contract_type()
    {
        return $this->belongsTo(ContractType::class);
    }

    public function contractType()
    {
        return $this->belongsTo(ContractType::class);
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function assignedTechnician()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get all documents associated with this maintenance request.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function documents()
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Get all visits associated with this maintenance request (polymorphic).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany
     */
    public function visits()
    {
        return $this->morphMany(Visit::class, 'visitable');
    }

    /**
     * Get all visits associated with this maintenance request (backward compatibility).
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function directVisits()
    {
        return $this->hasMany(Visit::class);
    }

    /**
     * Get all technician reports associated with this maintenance request.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function technicianReports()
    {
        return $this->hasMany(TechnicianReport::class);
    }

    /**
     * Get the latest technician report for this maintenance request.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function latestTechnicianReport()
    {
        return $this->hasOne(TechnicianReport::class)->latest();
    }

    /**
     * Get the PDF URL if the file exists.
     *
     * @return string|null
     */
    public function getPdfUrlAttribute(): ?string
    {
        if (!$this->pdf_file_path || !$this->pdf_disk) {
            return null;
        }

        $disk = Storage::disk($this->pdf_disk);

        if (!$disk->exists($this->pdf_file_path)) {
            return null;
        }

        return $disk->url($this->pdf_file_path);
    }

    /**
     * Check if a valid PDF exists for this maintenance request.
     *
     * @return bool
     */
    public function hasPdf(): bool
    {
        return $this->pdf_file_path
            && $this->pdf_disk
            && Storage::disk($this->pdf_disk)->exists($this->pdf_file_path);
    }

    /**
     * Check if the PDF needs to be regenerated.
     * Returns true if no PDF exists or if the maintenance request
     * has been updated since the last PDF generation.
     *
     * @return bool
     */
    public function needsPdfRegeneration(): bool
    {
        // No PDF exists
        if (!$this->hasPdf()) {
            return true;
        }

        // No generation timestamp recorded
        if (!$this->pdf_generated_at) {
            return true;
        }

        // Maintenance request updated after PDF generation
        return $this->updated_at > $this->pdf_generated_at;
    }

    /**
     * Get the storage disk for PDF files.
     * Uses environment configuration with fallback to 'public'.
     *
     * @return string
     */
    public static function getPdfDisk(): string
    {
        return config('filesystems.pdf_disk', 'public');
    }

    /**
     * Get a formatted display string for request number with client name.
     *
     * @return string
     */
    public function getRequestNumberWithClientAttribute(): string
    {
        return $this->request_number . ' - ' . ($this->client?->name ?? __('maintenance.unknown_client'));
    }

    /**
     * Generate the PDF file path for this maintenance request.
     *
     * @return string
     */
    public function generatePdfFilePath(): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "maintenance_request_{$this->id}_{$timestamp}.pdf";

        return "maintenance-requests/pdfs/{$filename}";
    }

    /**
     * Store PDF file information in the database.
     *
     * @param string $filePath
     * @param string|null $disk
     * @return bool
     */
    public function storePdfInfo(string $filePath, ?string $disk = null): bool
    {
        $disk = $disk ?? static::getPdfDisk();

        return $this->update([
            'pdf_file_path' => $filePath,
            'pdf_disk' => $disk,
            'pdf_generated_at' => now(),
        ]);
    }

    /**
     * Delete the stored PDF file and clear database references.
     *
     * @return bool
     */
    public function deletePdf(): bool
    {
        $deleted = true;

        // Delete the physical file if it exists
        if ($this->hasPdf()) {
            $deleted = Storage::disk($this->pdf_disk)->delete($this->pdf_file_path);
        }

        // Clear database references
        $this->update([
            'pdf_file_path' => null,
            'pdf_disk' => null,
            'pdf_generated_at' => null,
        ]);

        return $deleted;
    }

    /**
     * Get the PDF file size in bytes.
     *
     * @return int|null
     */
    public function getPdfFileSize(): ?int
    {
        if (!$this->hasPdf()) {
            return null;
        }

        return Storage::disk($this->pdf_disk)->size($this->pdf_file_path);
    }

    /**
     * Get the PDF file size in human-readable format.
     *
     * @return string|null
     */
    public function getPdfFileSizeFormatted(): ?string
    {
        $size = $this->getPdfFileSize();

        if (!$size) {
            return null;
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $power = floor(log($size, 1024));

        return round($size / pow(1024, $power), 2) . ' ' . $units[$power];
    }
}
