<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Contract extends Model
{
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function contract_type()
    {
        return $this->belongsTo(ContractType::class);
    }

    public function contractType()
    {
        return $this->belongsTo(ContractType::class);
    }

    public function maintenanceRequest()
    {
        return $this->belongsTo(MaintenanceRequest::class);
    }

    public function visits()
    {
        return $this->hasMany(Visit::class);
    }

    public function payments()
    {
        return $this->hasManyThrough(Payment::class, MaintenanceRequest::class);
    }

    public function documents()
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Get the number of completed visits for this contract.
     *
     * @return int
     */
    public function getCompletedVisitsCountAttribute(): int
    {
        return $this->visits()->where('status', 'completed')->count();
    }

    /**
     * Get the number of scheduled visits for this contract.
     *
     * @return int
     */
    public function getScheduledVisitsCountAttribute(): int
    {
        return $this->visits()->whereIn('status', ['scheduled', 'in_progress'])->count();
    }

    /**
     * Get the total visits count (completed + scheduled)
     *
     * @return int
     */
    public function getTotalVisitsCountAttribute(): int
    {
        return $this->completed_visits_count + $this->scheduled_visits_count;
    }

    /**
     * Get the number of remaining visits for this contract.
     *
     * @return int
     */
    public function getRemainingVisitsCountAttribute(): int
    {
        return max(0, $this->visits_included - $this->total_visits_count);
    }

    /**
     * Check if the contract can have more visits scheduled.
     *
     * @return bool
     */
    public function getCanScheduleMoreVisitsAttribute(): bool
    {
        return $this->remaining_visits_count > 0 && $this->is_active;
    }

    /**
     * Check if the contract is active.
     *
     * @return bool
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' && now()->between($this->start_date, $this->end_date);
    }

    /**
     * Check if the contract is expired.
     *
     * @return bool
     */
    public function getIsExpiredAttribute(): bool
    {
        return now()->isAfter($this->end_date);
    }

    /**
     * Get all technician reports for maintenance requests associated with this contract.
     */
    public function technicianReports()
    {
        return $this->hasManyThrough(
            \App\Models\TechnicianReport::class,
            \App\Models\MaintenanceRequest::class,
            'contract_id', // Foreign key on maintenance_requests table
            'maintenance_request_id', // Foreign key on technician_reports table
            'id', // Local key on contracts table
            'id' // Local key on maintenance_requests table
        );
    }
}
