<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Translatable\HasTranslations;

/**
 * TechnicianReport Model
 * 
 * Represents a report submitted by a technician for a maintenance request.
 * Includes work summary, findings, recommendations, and checklist items.
 */
class TechnicianReport extends Model
{
    use HasTranslations;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'maintenance_request_id',
        'technician_id',
        'work_summary',
        'findings',
        'recommendations',
        'issues_found',
        'parts_used',
        'time_spent_minutes',
        'status',
        'submitted_at',
        'attachments',
        'drawing_data',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'submitted_at' => 'datetime',
        'time_spent_minutes' => 'integer',
        'attachments' => 'array',
        'drawing_data' => 'array',
    ];

    /**
     * The attributes that are translatable.
     */
    public array $translatable = [
        'work_summary',
        'findings', 
        'recommendations',
        'issues_found',
        'parts_used',
    ];

    /**
     * Status constants for technician reports.
     */
    public const STATUS_DRAFT = 'draft';
    public const STATUS_SUBMITTED = 'submitted';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    /**
     * Get the maintenance request that this report belongs to.
     */
    public function maintenanceRequest(): BelongsTo
    {
        return $this->belongsTo(MaintenanceRequest::class);
    }

    /**
     * Get the technician who created this report.
     */
    public function technician(): BelongsTo
    {
        return $this->belongsTo(User::class, 'technician_id');
    }

    /**
     * Get all checklist items for this report (polymorphic relationship).
     */
    public function checklistItems(): MorphMany
    {
        return $this->morphMany(TechnicianReportChecklistItem::class, 'reportable');
    }

    /**
     * Get all documents associated with this report.
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Get all available status options.
     */
    public static function getStatusOptions(): array
    {
        return [
            self::STATUS_DRAFT => __('technician.resources.report.status_options.draft'),
            self::STATUS_SUBMITTED => __('technician.resources.report.status_options.submitted'),
            self::STATUS_APPROVED => __('technician.resources.report.status_options.approved'),
            self::STATUS_REJECTED => __('technician.resources.report.status_options.rejected'),
        ];
    }

    /**
     * Scope a query to only include reports by a specific technician.
     */
    public function scopeByTechnician($query, $technicianId)
    {
        return $query->where('technician_id', $technicianId);
    }

    /**
     * Scope a query to only include submitted reports.
     */
    public function scopeSubmitted($query)
    {
        return $query->where('status', self::STATUS_SUBMITTED);
    }

    /**
     * Check if the report can be edited.
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT]);
    }

    /**
     * Check if the report can be submitted.
     */
    public function canBeSubmitted(): bool
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * Submit the report.
     */
    public function submit(): bool
    {
        if (!$this->canBeSubmitted()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_SUBMITTED,
            'submitted_at' => now(),
        ]);

        return true;
    }

    /**
     * Get the completion percentage based on filled fields.
     */
    public function getCompletionPercentageAttribute(): int
    {
        $totalFields = 5; // work_summary, findings, recommendations, issues_found, parts_used
        $filledFields = 0;

        if (!empty($this->work_summary)) $filledFields++;
        if (!empty($this->findings)) $filledFields++;
        if (!empty($this->recommendations)) $filledFields++;
        if (!empty($this->issues_found)) $filledFields++;
        if (!empty($this->parts_used)) $filledFields++;

        return (int) (($filledFields / $totalFields) * 100);
    }
}
