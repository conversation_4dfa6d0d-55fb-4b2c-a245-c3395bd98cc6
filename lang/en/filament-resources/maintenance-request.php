<?php

return [
    'fields' => [
        'request_number' => 'Request Number',
        'request_number_help' => 'Request number is generated automatically',
        'contract_type_id' => 'Contract Type',
        'contract_type_help' => 'Select the required contract type',
        'contract_id' => 'Contract',
        'client_id' => 'Client',
        'client_help' => 'Search for client by name, phone, or email',
        'title' => 'Title',
        'title_help' => 'Brief title for the maintenance request',
        'description' => 'Description',
        'description_help' => 'Detailed description of the maintenance request',

        'status' => 'Status',
        'status_help' => 'Current status of the maintenance request',
        'request_date' => 'Request Date',
        'completion_date' => 'Completion Date',
        'completion_date_help' => 'Expected completion date for the request',
        'assigned_to' => 'Assigned To',
        'assigned_to_help' => 'Technician responsible for executing the request',
        'notes' => 'Notes',
        'notes_help' => 'Additional notes about the maintenance request',
        'contract_price' => 'Contract Price',
        'contract_price_help' => 'Specified price for the maintenance contract',
        'price_notes' => 'Price Notes',
        'client_phone' => 'Client Phone',
        'visits_included' => 'Visits Included',
        'created_at' => 'Created At',
        'completed_at' => 'Actual Completion Date',
    ],

    'status_options' => [
        'new' => 'New',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
    ],

    'columns' => [
        'request_number' => 'Request Number',
        'client' => ['name' => 'Client'],
        'contract' => ['contract_number' => 'Contract Number'],
        'title' => 'Title',
        'status' => 'Status',
        'request_date' => 'Request Date',
        'assignedTechnician' => ['name' => 'Assigned To'],
    ],

    'filters' => [
        'client_id' => 'Client',
        'contract_id' => 'Contract',
        'status' => 'Status',
        'assigned_to' => 'Assigned To',
        'created_from' => 'From Date',
        'created_from_placeholder' => 'Select start date',
        'created_until' => 'To Date',
        'created_until_placeholder' => 'Select end date',
    ],

    'actions' => [
        'view' => 'View',
        'view_modal_heading' => 'View Maintenance Request :number',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'delete_modal_heading' => 'Delete Maintenance Request',
        'delete_modal_description' => 'Are you sure you want to delete this maintenance request? This action cannot be undone.',
        'delete_confirm' => 'Yes, Delete',
        'update_price' => 'Set Price',
        'create_contract' => 'Create Contract',
        'create' => 'Create',
        'create_modal_heading' => 'Create New Maintenance Request',
        'create_another' => 'Create Another',
        'save' => 'Save',
        'cancel' => 'Cancel',
        'refresh_cache' => 'Refresh Data',
    ],

    'bulk_actions' => [
        'assign_technician' => 'Assign Technician',
        'update_status' => 'Update Status',
        'delete' => 'Delete Selected',
    ],

    'sections' => [
        'main_details' => 'Main Details',
        'main_details_description' => 'Basic information for the maintenance request',
        'additional_details' => 'Additional Details',
        'additional_details_description' => 'Additional information and detailed description of the request',
        'request_details' => 'Request Details',
        'client_contract' => 'Client & Contract Information',
        'assignment_financials' => 'Assignment & Financials',
        'assignment_financial' => 'Assignment & Financial',
    ],

    'placeholders' => [
        'no_contract' => 'No contract',
        'unassigned' => 'Unassigned',
        'not_set' => 'Not set',
        'not_completed' => 'Not completed yet',
    ],

    'messages' => [
        'request_number_copied' => 'Request number copied',
    ],

    'notifications' => [
        'price_updated' => 'Price updated successfully',
        'price_updated_body' => 'Contract price set to :amount SAR',
        'price_update_failed' => 'Failed to update price',
        'price_update_failed_body' => 'An error occurred while updating the price. Please try again.',
        'contract_created' => 'Contract created successfully',
        'contract_created_body' => 'Contract number :number created successfully',
        'contract_creation_failed' => 'Failed to create contract',
        'contract_creation_failed_body' => 'An error occurred while creating the contract. Please try again.',
        'bulk_assigned' => 'Technician assigned to selected requests',
        'bulk_status_updated' => 'Status updated for selected requests',
        'created_successfully' => 'Maintenance request created successfully',
        'created_successfully_body' => 'Maintenance request number :number created successfully',
        'creation_failed' => 'Failed to create maintenance request',
        'creation_failed_body' => 'An error occurred while creating the maintenance request. Please try again.',
        'updated_successfully' => 'Maintenance request updated successfully',
        'updated_successfully_body' => 'Maintenance request number :number updated successfully',
        'update_failed' => 'Failed to update maintenance request',
        'update_failed_body' => 'An error occurred while updating the maintenance request. Please try again.',
        'cache_refreshed' => 'Data refreshed successfully',
    ],

    'empty_state' => [
        'heading' => 'No maintenance requests',
        'description' => 'Start by creating a new maintenance request.',
    ],

    'global_search' => [
        'client' => 'Client',
        'status' => 'Status',
        'created' => 'Created Date',
    ],

    'untitled' => 'Untitled',

    'pages' => [
        'create' => [
            'title' => 'Create New Maintenance Request',
            'heading' => 'Create Maintenance Request',
            'subheading' => 'Enter details for the new maintenance request',
        ],
        'edit' => [
            'title' => 'Edit Maintenance Request :number',
            'heading' => 'Edit Maintenance Request :number',
            'subheading' => 'Client: :client | Status: :status',
        ],
        'list' => [
            'title' => 'Maintenance Requests',
            'heading' => 'Manage Maintenance Requests',
        ],
        'view' => [
            'title' => 'View Maintenance Request :number',
            'heading' => 'Maintenance Request Details :number',
        ],
    ],

    'tabs' => [
        'all' => 'All',
        'new' => 'New',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
        'overdue' => 'Overdue',
        'notes_details' => 'Notes & Details',
        'activity_timeline' => 'Activity Timeline',
        'financial_summary' => 'Financial Summary',
        'related_records' => 'Related Records',
    ],

    'relations' => [
        'visits' => 'Visits',
        'services' => 'Services',
        'payments' => 'Payments',
        'documents' => 'Documents',
    ],

    'fields' => [
        'request_number' => 'Request Number',
        'request_number_help' => 'Request number is generated automatically',
        'contract_type_id' => 'Contract Type',
        'contract_type_help' => 'Select the required contract type',
        'contract_id' => 'Contract',
        'client_id' => 'Client',
        'client_help' => 'Search for client by name, phone, or email',
        'title' => 'Title',
        'title_help' => 'Brief title for the maintenance request',
        'description' => 'Description',
        'description_help' => 'Detailed description of the maintenance request',
        'technician' => 'Technician',
        'assignment_notes' => 'Assignment Notes',
        'status' => 'Status',
        'status_help' => 'Current status of the maintenance request',
        'request_date' => 'Request Date',
        'completion_date' => 'Completion Date',
        'completion_date_help' => 'Expected completion date for the request',
        'assigned_to' => 'Assigned To',
        'assigned_to_help' => 'Technician responsible for executing the request',
        'notes' => 'Notes',
        'notes_help' => 'Additional notes about the maintenance request',
        'contract_price' => 'Contract Price',
        'contract_price_help' => 'Specified price for the maintenance contract',
        'price_notes' => 'Price Notes',
        'client_phone' => 'Client Phone',
        'visits_included' => 'Visits Included',
        'created_at' => 'Created At',
        'completed_at' => 'Actual Completion Date',
        'assigned_technician' => 'Assigned Technician',
        'price' => 'Price',
        'payment_status' => 'Payment Status',
        'contract_period' => 'Contract Period',
        'contract_start_date' => 'Contract Start Date',
        'contract_end_date' => 'Contract End Date',
        'contract_type_description' => 'Contract Type Description',
        'contract_benefits' => 'Contract Benefits',
        'total_amount_required' => 'Total Required Amount',
        'amount_paid' => 'Amount Paid',
        'amount_remaining' => 'Remaining Amount',
        'payment_instructions' => 'Payment Instructions',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'contract_terms' => 'Contract Terms',
        'contract_status' => 'Contract Status',
    ],

    'currency' => [
        'sar' => 'SAR',
    ],

    'payment_status' => [
        'pending' => 'pending',
        'paid' => 'paid',
        'overdue' => 'overdue',
        'no_payments' => 'No payments',
        'unpaid' => 'Unpaid',
        'partially_paid' => 'Partially paid',
        'fully_paid' => 'Fully paid',
    ],

    'contract_status' => [
        'pending' => 'Pending',
        'active' => 'Active',
        'expired' => 'Expired',
        'cancelled' => 'Cancelled',
    ],

    'messages' => [
        'request_number_copied' => 'Request number copied',
        'activity_timeline_placeholder' => 'Activity timeline will be displayed here.',
    ],

    'wizard' => [
        'step1' => [
            'title' => 'Select Contract Type',
            'heading' => 'Choose Maintenance Contract Type',
            'description' => 'Select the contract type that suits your needs',
            'section_title' => 'Available Contract Types',
            'section_description' => 'Select the contract type that suits your needs',
            'contract_types_title' => 'Available Contract Types',
            'next_button' => 'Next',
            'loading' => 'Loading...',
        ],

        'step2' => [
            'title' => 'Contract Details',
            'heading' => 'Enter Contract Details',
            'description' => 'Fill in the required information for the maintenance contract',
            'section_title' => 'Company and Contact Information',
            'section_description' => 'Enter your company details and contact information',
            'previous_button' => 'Previous',
            'next_button' => 'Next',
            'loading' => 'Loading...',

            'fields' => [
                'company_name' => 'Company Name',
                'contact_name' => 'Contact Name',
                'phone' => 'Phone Number',
                'email' => 'Email Address',
                'address' => 'Address',
            ],
        ],

        'step3' => [
            'title' => 'Review and Confirm',
            'heading' => 'Review Contract Details',
            'description' => 'Review all details before submitting the request',
            'previous_button' => 'Previous',
            'submit_button' => 'Submit Request',
            'loading' => 'Submitting...',
            'contract_summary_title' => 'Contract Summary',
            'summary_section_title' => 'Contract Summary',
            'terms_section_title' => 'Terms and Conditions',

            'summary' => [
                'duration_unit' => 'months',
            ],

            'fields' => [
                'terms_agreement' => 'I agree to the terms and conditions',
                'terms_agreement_helper' => 'Click here to view terms and conditions',
            ],
        ],

        'success' => [
            'title' => 'Request Created Successfully',
            'heading' => 'Maintenance Request Created Successfully!',
            'step_heading' => 'Request Created Successfully',
            'step_description' => 'Your maintenance request has been submitted successfully',
            'main_heading' => 'Maintenance Request Created Successfully!',
            'request_number_label' => 'Request Number',
            'current_status_label' => 'Current Status',
            'next_steps_title' => 'Next Steps',
            'details_section_title' => 'Request Details',
            'timeline_title' => 'Expected Timeline',

            'status_descriptions' => [
                'new' => 'Your request has been created successfully and is now in the queue for review. We will contact you soon to confirm the details.',
                'pending' => 'Your request is being reviewed by our specialized team.',
                'assigned' => 'A specialized technician has been assigned to your request and will contact you soon.',
                'in_progress' => 'Work is in progress on your request.',
                'completed' => 'Your request has been completed successfully.',
                'canceled' => 'Your request has been canceled.',
                'default' => 'Request status: :status',
            ],

            'next_steps' => [
                'new' => 'Our team will review your request within 1-3 business days and will contact you to confirm details and schedule the visit.',
                'pending' => 'We will contact you within 24 hours to confirm the details.',
                'assigned' => 'The assigned technician will contact you to schedule a suitable visit time.',
                'in_progress' => 'Work will be completed according to the agreed timeline.',
                'completed' => 'Thank you for choosing our services.',
                'default' => 'We will contact you soon regarding your request.',
            ],

            'actions' => [
                'new_request' => 'New Request',
                'view_requests' => 'View My Requests',
                'back_to_dashboard' => 'Back to Dashboard',
            ],

            'details' => [
                'request_number' => 'Request Number',
                'status' => 'Status',
                'created_date' => 'Created Date',
                'contract_type' => 'Contract Type',
                'visits_included' => 'Visits Included',
                'client' => 'Client',
                'not_specified' => 'Not specified',
            ],
        ],
    ],
];
