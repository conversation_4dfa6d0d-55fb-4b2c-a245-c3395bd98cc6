<?php

return [
    'fields' => [
        'visit_date' => 'Visit Date',
        'status' => 'Status',
        'notes' => 'Notes',
        'technician_id' => 'Technician',
        'contract_id' => 'Contract',
        'maintenance_request_id' => 'Maintenance Request',
        'visitable_type' => 'Related Entity Type',
        'visitable_id' => 'Related Entity',
        'scheduled_at' => 'Scheduled At',
        'scheduled_date' => 'Scheduled Date',
        'scheduled_time' => 'Scheduled Time',
        'started_at' => 'Started At',
        'completed_at' => 'Completed At',
        'findings' => 'Findings',
        'actions_taken' => 'Actions Taken',
        'recommendations' => 'Recommendations',
        'start_time' => 'Start Time',
        'end_time' => 'End Time',
        'duration' => 'Duration',
    ],

    'visitable_types' => [
        'contract' => 'Contract',
        'maintenance_request' => 'Maintenance Request',
    ],

    'sections' => [
        'related_entity' => 'Related Entity',
        'visit_details' => 'Visit Details',
        'visit_report' => 'Visit Report',
    ],

    'status_options' => [
        'scheduled' => 'Scheduled',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
        'canceled' => 'Canceled',
        'rescheduled' => 'Rescheduled',
    ],

    'columns' => [
        'related_entity' => 'Related Entity',
        'visit_date' => 'Visit Date',
        'status' => 'Status',
        'client_name' => 'Client Name',
        'technician' => ['name' => 'Technician'],
        'contract' => ['contract_number' => 'Contract Number'],
        'maintenanceRequest' => [
            'request_number' => 'Request Number',
            'client' => ['name' => 'Client'],
        ],
        'scheduled_at' => 'Scheduled At',
        'started_at' => 'Started At',
        'completed_at' => 'Completed At',
        'findings' => 'Findings',
        'actions_taken' => 'Actions Taken',
        'start_time' => 'Start Time',
        'end_time' => 'End Time',
    ],

    'placeholders' => [
        'no_client' => 'No Client',
        'no_technician' => 'Not Assigned',
        'no_contract' => 'Not Linked to Contract',
        'no_maintenance_request' => 'Not Linked to Maintenance Request',
        'no_related_entity' => 'Not Linked to Any Entity',
    ],

    'related_entity_formats' => [
        'contract' => 'Contract: :number',
        'maintenance_request' => 'Maintenance Request: :number',
    ],

    'filters' => [
        'status' => 'Status',
        'technician_id' => 'Technician',
        'contract_id' => 'Contract',
        'visitable_type' => 'Related Entity Type',
    ],

    'actions' => [
        'view' => 'View',
        'edit' => 'Edit',
        'delete' => 'Delete',
        'reschedule' => 'Reschedule',
    ],
];
