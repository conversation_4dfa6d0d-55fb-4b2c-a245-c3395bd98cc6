<?php

return [
    'navigation_label' => 'Maintenance Contracts',
    'plural_label' => 'Maintenance Contracts',
    'label' => 'Maintenance Contract',
    
    'columns' => [
        'request_number' => 'Request Number',
        'client_name' => 'Client Name',
        'contract_type' => 'Contract Type',
        'visits_count' => 'Number of Visits',
        'status' => 'Status',
        'created_at' => 'Request Date',
    ],
    
    'status_options' => [
        'new' => 'New Request',
        'pending' => 'Under Review',
        'assigned' => 'Assigned',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'canceled' => 'Canceled',
    ],
    
    'filters' => [
        'status' => 'Status',
        'contract_type' => 'Contract Type',
    ],
    
    'actions' => [
        'view' => 'View',
        'new_contract' => 'New Contract Request',
        'print_request' => 'Print Request',
        'download_pdf' => 'Download PDF',
        'download_contract' => 'Download Contract',
        'contact_support' => 'Contact Support',
        'cancel_request' => 'Cancel Request',
        'close' => 'Close',
    ],
    
    'messages' => [
        'request_number_copied' => 'Request number copied',
        'phone_copied' => 'Phone number copied',
        'email_copied' => 'Email copied',
        'contract_number_copied' => 'Contract number copied',
        'payment_instructions' => 'Payment details will be sent via email or phone after request approval.',
    ],
    
    'placeholders' => [
        'not_specified' => 'Not specified',
        'not_assigned' => 'Not assigned yet',
        'not_priced' => 'Price not set yet',
        'no_contract' => 'No contract',
        'no_contract_created' => 'Contract not created yet',
        'no_payments' => 'No payments',
        'not_set' => 'Not set',
        'not_set_yet' => 'Not set yet',
        'no_title' => 'No title',
        'no_notes' => 'No notes',
        'no_notes_available' => 'No notes available',
        'no_description_available' => 'No description available',
        'no_benefits_specified' => 'No benefits specified',
        'no_terms_set' => 'Terms not set yet',
    ],
    
    'search' => [
        'client' => 'Client',
        'type' => 'Type',
        'date' => 'Date',
    ],
    
    'cards' => [
        'status' => 'Request Status',
        'request_number' => 'Request Number',
        'contract_type' => 'Contract Type',
        'creation_date' => 'Request Date',
    ],
    
    'sections' => [
        'request_details' => 'Request Details',
        'client_contract' => 'Client and Contract Information',
        'assignment_financial' => 'Assignment and Financial',
        'progress_timeline' => 'Request Processing Steps',
        'additional_info' => 'Additional Information',
        'contract_details' => 'Contract Details',
    ],
    
    'fields' => [
        'request_number' => 'Request Number',
        'title' => 'Request Title',
        'created_at' => 'Creation Date',
        'status' => 'Status',
        'visits_included' => 'Number of Included Visits',
        'notes' => 'Notes',
        'client_id' => 'Client',
        'client_phone' => 'Phone Number',
        'client_email' => 'Email',
        'contract_number' => 'Contract Number',
        'contract_type' => 'Contract Type',
        'contract_status' => 'Contract Status',
        'assigned_technician' => 'Assigned Technician',
        'price' => 'Price',
        'payment_status' => 'Payment Status',
        'contract_period' => 'Contract Period',
        'contract_start_date' => 'Contract Start Date',
        'contract_end_date' => 'Contract End Date',
        'contract_type_description' => 'Contract Type Description',
        'contract_benefits' => 'Contract Benefits',
        'total_amount_required' => 'Total Required Amount',
        'amount_paid' => 'Amount Paid',
        'amount_remaining' => 'Remaining Amount',
        'payment_instructions' => 'Payment Instructions',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'contract_terms' => 'Contract Terms',
    ],
    
    'units' => [
        'months' => 'month',
        'riyal' => 'riyal',
    ],
    
    'tabs' => [
        'notes_details' => 'Notes and Details',
        'payments' => 'Payments',
        'linked_contract' => 'Linked Contract',
    ],
    
    'aria' => [
        'status_label' => 'Request status',
        'request_number_label' => 'Request number',
        'progress_timeline' => 'Request processing steps',
        'notes' => 'Request notes',
        'contract_type_description' => 'Contract type description',
        'contract_benefits' => 'Contract benefits',
        'notes_details_tab' => 'Notes and details tab',
        'total_amount_required' => 'Total required amount',
        'amount_paid' => 'Amount paid',
        'amount_remaining' => 'Remaining amount',
        'payment_instructions' => 'Payment instructions',
        'payments_tab' => 'Payments tab',
        'contract_number' => 'Contract number',
        'contract_start_date' => 'Contract start date',
        'contract_end_date' => 'Contract end date',
        'contract_status' => 'Contract status',
        'contract_terms' => 'Contract terms',
        'linked_contract_tab' => 'Linked contract tab',
        'generate_pdf' => 'Generate PDF file',
        'download_contract' => 'Download contract',
        'contact_support' => 'Contact support',
        'contract_type_label' => 'Contract type',
        'creation_date_label' => 'Request creation date',
    ],
    
    'pages' => [
        'view' => [
            'title' => 'View Maintenance Request: :number',
            'heading' => 'Maintenance Request Number: :number',
        ],
    ],
    
    'timeline' => [
        'request_created' => 'Request created',
        'under_review' => 'Under review',
        'review_completed' => 'Request review completed',
        'awaiting_review' => 'Awaiting review',
        'technician_assigned' => 'Technician assigned',
        'awaiting_assignment' => 'Awaiting technician assignment',
        'work_in_progress' => 'Work in progress',
        'awaiting_work_start' => 'Awaiting work start',
        'contract_created' => 'Contract created',
        'contract_number' => 'Contract number',
        'awaiting_contract_creation' => 'Awaiting contract creation',
        'contract_creation' => 'Contract creation',
        'processing_completed' => 'Maintenance completed',
        'processing_completion' => 'Maintenance completion',
        'awaiting_final_procedures' => 'Awaiting maintenance work completion',
    ],
    
    'tooltips' => [
        'print_request' => 'Preview and print maintenance request',
        'download_contract' => 'Download contract file',
        'contact_support' => 'Send inquiry to technical support',
        'cancel_request' => 'Permanently cancel request',
    ],
    
    'support' => [
        'inquiry_type_label' => 'Inquiry Type',
        'inquiry_type_placeholder' => 'Select inquiry type',
        'inquiry_types' => [
            'status' => 'Inquiry about request status',
            'payment' => 'Inquiry about payment',
            'contract' => 'Inquiry about contract',
            'technical' => 'Technical inquiry',
            'other' => 'Other',
        ],
        'message_label' => 'Your message',
        'message_placeholder' => 'Write your inquiry here...',
        'message_helper' => 'Minimum 10 characters, maximum 1000 characters',
    ],
    
    'cancel' => [
        'modal_heading' => 'Cancel Request',
        'modal_description' => 'Are you sure you want to cancel this request? This action cannot be undone.',
        'submit_label' => 'Yes, cancel request',
        'cancel_label' => 'Go back',
    ],
    
    'notifications' => [
        'pdf_loaded' => 'Saved PDF file loaded',
        'pdf_loaded_body' => 'Used saved version of PDF file',
        'pdf_generated' => 'PDF file successfully generated',
        'pdf_generated_body' => 'You can now preview or download the file',
        'pdf_generated_temp' => 'Temporary PDF file generated',
        'pdf_storage_failed' => 'File generated but failed to save. You can preview and download it now.',
        'pdf_generation_failed' => 'PDF file generation failed',
        'pdf_generation_failed_body' => 'Error occurred while generating PDF file. Please try again.',
        'support_sent_title' => 'Your message has been sent',
        'support_sent_body' => 'We will contact you within 24 hours via email or phone',
        'support_failed_title' => 'Failed to send message',
        'support_failed_body' => 'Error occurred while sending your message. Please try again.',
        'cancel_success_title' => 'Request canceled',
        'cancel_success_body' => 'Maintenance request successfully canceled',
        'cancel_failed_title' => 'Failed to cancel request',
        'cancel_failed_body' => 'Error occurred while canceling request. Please try again.',
    ],

    'view' => [
        'created_on' => 'Created on',
        'progress_tracker' => 'Request Progress Tracker',
        'quick_actions' => 'Quick Actions',

        'progress' => [
            'request_created' => 'Request Created',
            'review' => 'Review',
            'review_completed' => 'Review Completed',
            'under_review' => 'Under Review',
            'waiting' => 'Waiting',
            'technician_assignment' => 'Technician Assignment',
            'technician_assigned' => 'Technician Assigned',
            'assigning_technician' => 'Assigning Technician',
            'work_execution' => 'Work Execution',
            'work_completed' => 'Work Completed',
            'work_in_progress' => 'Work in Progress',
            'work_starting_soon' => 'Starting Soon',
            'completion' => 'Completion',
            'completed' => 'Completed',
        ],

        'actions' => [
            'contact_us' => 'Contact Us',
            'contact_description' => 'For inquiries about your request status',
            'contact_now' => 'Contact Now',
            'update_data' => 'Update Data',
            'update_description' => 'Edit request information',
            'edit' => 'Edit',
            'print_request' => 'Print Request',
            'print_description' => 'Get a printed copy',
            'print' => 'Print',
        ],

        'help' => [
            'title' => 'Need Help?',
            'useful_info' => 'Useful Information',
            'processing_time_label' => 'Processing Time',
            'processing_time_text' => 'Requests are usually reviewed within 1-3 business days',
            'technician_assignment_label' => 'Technician Assignment',
            'technician_assignment_text' => 'A specialized technician will be assigned after request approval',
            'work_execution_label' => 'Work Execution',
            'work_execution_text' => 'The technician will contact you to schedule a suitable maintenance time',
            'communication_label' => 'Communication',
            'communication_text' => 'We will contact you via registered phone or email',
            'contract_label' => 'Contract',
            'contract_text' => 'Contract will be automatically created after request approval',
            'working_hours' => 'Sunday - Thursday: 8:00 AM - 5:00 PM',
        ],
    ],

    'wizard' => [
        'steps' => [
            'contract_selection' => 'Contract Type Selection',
            'client_data' => 'Client Information',
            'review' => 'Review',
            'confirmation' => 'Confirmation',
        ],

        'step1' => [
            'heading' => 'Choose the Appropriate Contract Type',
            'description' => 'Click on the contract type that suits your needs to automatically proceed to the next step',
        ],

        'step2' => [
            'heading' => 'Step 2 of 4: Client Information',
            'description' => 'Please fill in all required information accurately',
        ],

        'step3' => [
            'heading' => 'Step 3 of 4: Review and Confirmation',
            'description' => 'Please review the data and agree to the terms and conditions',
            'contract_summary' => 'Contract Summary',
            'contract_info' => 'Contract Information',
            'client_info' => 'Client Information',
            'contract_type' => 'Contract Type',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'contract_duration' => 'Contract Duration',
            'visits_count' => 'Number of Visits',
            'company_name' => 'Company Name',
            'contact_name' => 'Contact Name',
            'phone' => 'Phone Number',
            'email' => 'Email',
            'address' => 'Address',
            'contract_value' => 'Contract Value',
            'cost_after_visit' => 'Cost will be determined after technical visit',
            'visits_included' => 'Number of included visits',
            'terms_conditions' => 'Terms and Conditions',
            'terms' => [
                'term1' => 'The client acknowledges that all information provided in this request is accurate and correct.',
                'term2' => 'The client agrees to pay the contract value according to the specified payment method and frequency.',
                'term3' => 'The final contract will be signed after coordination with the client and confirmation of all details.',
                'term4' => 'The contract period begins from the final signing date.',
                'term5' => 'The service provider is committed to performing maintenance visits according to the agreed schedule.',
                'term6' => 'Contract terms can be modified with the agreement of both parties.',
                'term7' => 'In case of early contract termination, fees will be calculated according to the period used.',
                'term8' => 'Contract renewal is subject to the agreement of both parties unless automatic renewal is activated.',
                'term9' => 'Additional visits beyond contract limits will be at additional cost according to contract type.',
                'agree' => 'Agree',
            ],
        ],

        'navigation' => [
            'previous' => 'Previous',
            'next' => 'Next',
            'confirm_submit' => 'Confirm and Submit',
        ],

        'contract_types' => [
            'more_benefits' => 'and :count more benefits...',
            'contract_duration' => 'Contract Duration',
            'months' => 'month',
            'visits_count' => 'Number of Visits',
            'visits' => 'visit',
            'loading_next_step' => 'Moving to next step...',
        ],

        'summary' => [
            'contract_value' => 'Contract Value',
            'cost_after_visit' => 'Cost will be determined after technical visit',
            'visits_included' => 'Number of included visits',
            'contract_info' => 'Contract Information',
            'client_info' => 'Client Information',
            'contract_type' => 'Contract Type',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'contract_duration' => 'Contract Duration',
            'visits_count' => 'Number of Visits',
            'company_name' => 'Company Name',
            'contact_name' => 'Contact Name',
            'phone' => 'Phone Number',
            'email' => 'Email',
            'address' => 'Address',
        ],

        'success' => [
            'step_heading' => 'Request Created Successfully',
            'step_description' => 'Your maintenance request has been submitted successfully',
            'main_heading' => 'Maintenance Request Created Successfully!',
            'request_number_label' => 'Request Number',
            'current_status_label' => 'Current Status',
            'next_steps_title' => 'Next Steps',
            'details_section_title' => 'Request Details',
            'timeline_title' => 'Expected Timeline',

            'status_descriptions' => [
                'new' => 'Your request has been created successfully and is now in the queue for review. We will contact you soon to confirm the details.',
                'pending' => 'Your request is being reviewed by our specialized team.',
                'assigned' => 'A specialized technician has been assigned to your request and will contact you soon.',
                'in_progress' => 'Work is in progress on your request.',
                'completed' => 'Your request has been completed successfully.',
                'canceled' => 'Your request has been canceled.',
                'default' => 'Request status: :status',
            ],

            'next_steps' => [
                'new' => 'Our team will review your request within 1-3 business days and will contact you to confirm details and schedule the visit.',
                'pending' => 'We will contact you within 24 hours to confirm the details.',
                'assigned' => 'The assigned technician will contact you to schedule a suitable visit time.',
                'in_progress' => 'Work will be completed according to the agreed timeline.',
                'completed' => 'Thank you for choosing our services.',
                'default' => 'We will contact you soon regarding your request.',
            ],

            'actions' => [
                'new_request' => 'New Request',
                'view_requests' => 'View My Requests',
                'back_to_dashboard' => 'Back to Dashboard',
            ],

            'details' => [
                'request_number' => 'Request Number',
                'status' => 'Status',
                'created_date' => 'Created Date',
                'contract_type' => 'Contract Type',
                'visits_included' => 'Visits Included',
                'client' => 'Client',
            ],
        ],
    ],
];
